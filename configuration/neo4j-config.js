const neo4j = require('neo4j-driver');
const config = require('./config');
const logger = require('../utils/logger');

let driver;
let isInitialized = false;

const initializeNeo4j = async (retries = 3, delay = 5000) => {
    if (isInitialized) {
        return driver;
    }

    for (let attempt = 1; attempt <= retries; attempt++) {
        try {
            const uri = process.env.NEO4J_URI || 'neo4j://localhost:7687';
            const username = process.env.NEO4J_USERNAME || 'neo4j';
            const password = process.env.NEO4J_PASSWORD || 'newpassword123';

            logger.info(`Attempting to connect to Neo4j (attempt ${attempt}/${retries})...`);
            logger.info(`Connection URI: ${uri}`);

            driver = neo4j.driver(
                uri,
                neo4j.auth.basic(username, password),
                {
                    maxConnectionLifetime: 3 * 60 * 60 * 1000, // 3 hours
                    maxConnectionPoolSize: 50,
                    connectionAcquisitionTimeout: 2 * 60 * 1000, // 2 minutes
                    disableLosslessIntegers: true,
                    logging: {
                        level: 'info',
                        logger: (level, message) => {
                            logger.info(`Neo4j ${level}: ${message}`);
                        }
                    }
                }
            );

            // Verify connection
            const serverInfo = await driver.verifyConnectivity();
            logger.info('Neo4j connected successfully ✅');
            logger.info(`Connected to Neo4j ${serverInfo.version} at ${uri}`);
            
            isInitialized = true;
            return driver;
        } catch (err) {
            logger.error(`Neo4j connection attempt ${attempt} failed:`, err);
            logger.error('Error details:', {
                message: err.message,
                code: err.code,
                cause: err.cause
            });

            if (attempt === retries) {
                logger.error('All connection attempts failed. Please check if Neo4j is running and accessible.');
                throw new Error(`Failed to connect to Neo4j after ${retries} attempts: ${err.message}`);
            }

            logger.info(`Retrying in ${delay/1000} seconds...`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
};

const getSession = () => {
    if (!driver || !isInitialized) {
        throw new Error('Neo4j driver not initialized. Please call initializeNeo4j() first.');
    }
    return driver.session({
        database: process.env.NEO4J_DATABASE || 'neo4j',
        defaultAccessMode: neo4j.session.WRITE
    });
};

const closeDriver = async () => {
    if (driver) {
        await driver.close();
        isInitialized = false;
        logger.info('Neo4j connection closed');
    }
};

// Handle process termination
process.on('SIGINT', async () => {
    await closeDriver();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    await closeDriver();
    process.exit(0);
});

module.exports = {
    initializeNeo4j,
    getDriver: () => driver,
    getSession,
    closeDriver
};