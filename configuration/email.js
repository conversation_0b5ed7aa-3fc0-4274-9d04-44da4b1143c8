const Sib = require("sib-api-v3-sdk")
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });
module.exports = () => {
const client = Sib.ApiClient.instance

const apiKey = client.authentications["api-key"]
apiKey.apiKey =  process.env.MAIL_API_KEY
const tranEmailApi = new Sib.TransactionalEmailsApi()

return tranEmailApi

};


// //configure/email.js :
// const sgMail = require('@sendgrid/mail');
// const path = require('path');
// require('dotenv').config({ path: path.join(__dirname, '../.env') });

// // Configure SendGrid with API key
// sgMail.setApiKey(process.env.SENDGRID_API_KEY);

// module.exports = sgMail;