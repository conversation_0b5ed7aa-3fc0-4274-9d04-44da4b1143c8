
const httpStatus = require('http-status');
const ApiError = require("../utils/ApiError");
// const User  = require("../modules/user/models/user.model");
// const Token = require();
const{Token , User , Role} = require("../models")
const config = require("../configuration/config");

module.exports.authorize = async (req, res, next) => {
    try {
        // console.log(req.headers)
        // Check if the token (name in this case) is present in the request headers
        if (!req.headers.token) {
            return next(new ApiError(httpStatus.BAD_REQUEST, "Token missing"));
        }

        // const payload = jwt.verify(req.headers.token, config.jwt.secret);

        const tokenDoc = await Token.findOne({where:{token: req.headers.token}})

        // console.log(tokenDoc)
        // console.log("tokenssssssssssssss",tokenDoc)
        if(!tokenDoc) {
            return next(new ApiError(httpStatus.BAD_REQUEST, "User not found"));
        }
        console.log(tokenDoc)


        // Find the user based on the provided token/name
        const user = await User.findOne({
            where: { id: tokenDoc.user_id },
            include: [ {
                model: Role,
                as: 'userRole', // Must match alias in association
                attributes: ['role', 'roleDisplayName', 'desc']
            }]
        });
        // console.log("userssssssssssssss", user)
        if (!user) {
            return next(new ApiError(httpStatus.BAD_REQUEST, "User not found"));
        }

        // Attach user info to the request object for use in the next middleware or route handler
        req.user = {...user.dataValues , token:req.headers.token};
        next(); // Proceed to the next middleware
    } catch (error) {
        console.log(error)
        // Pass any unexpected errors to the error handling middleware
        next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, "Internal server error"));
    }
};
