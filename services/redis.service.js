const Redis = require('ioredis');
const config = require('../configuration/config');
const logger = require('../utils/logger');

// Create Redis client
const client = new Redis({
    host: config.redis.host,
    port: config.redis.port,
    password: config.redis.password,
    retryStrategy: (times) => {
        const delay = Math.min(times * 50, 2000);
        return delay;
    }
});

// Event handlers
client.on('error', (err) => {
    logger.error('Redis Client Error:', err);
});

client.on('connect', () => {
    logger.info('Redis Client Connected');
});

// Redis service functions
const set = async (key, value, ttl = config.redis.ttl) => {
    try {
        const stringValue = JSON.stringify(value);
        if (ttl) {
            await client.setex(key, ttl, stringValue);
        } else {
            await client.set(key, stringValue);
        }
        return true;
    } catch (error) {
        logger.error('Redis Set Error:', error);
        return false;
    }
};

const get = async (key) => {
    try {
        const value = await client.get(key);
        return value ? JSON.parse(value) : null;
    } catch (error) {
        logger.error('Redis Get Error:', error);
        return null;
    }
};

const del = async (key) => {
    try {
        await client.del(key);
        return true;
    } catch (error) {
        logger.error('Redis Delete Error:', error);
        return false;
    }
};

const exists = async (key) => {
    try {
        return await client.exists(key);
    } catch (error) {
        logger.error('Redis Exists Error:', error);
        return false;
    }
};

const setHash = async (hashKey, field, value) => {
    try {
        const stringValue = JSON.stringify(value);
        await client.hset(hashKey, field, stringValue);
        return true;
    } catch (error) {
        logger.error('Redis SetHash Error:', error);
        return false;
    }
};

const getHash = async (hashKey, field) => {
    try {
        const value = await client.hget(hashKey, field);
        return value ? JSON.parse(value) : null;
    } catch (error) {
        logger.error('Redis GetHash Error:', error);
        return null;
    }
};

const getAllHash = async (hashKey) => {
    try {
        const hash = await client.hgetall(hashKey);
        if (!hash) return null;
        
        const result = {};
        for (const [key, value] of Object.entries(hash)) {
            result[key] = JSON.parse(value);
        }
        return result;
    } catch (error) {
        logger.error('Redis GetAllHash Error:', error);
        return null;
    }
};

const deleteHash = async (hashKey, field) => {
    try {
        await client.hdel(hashKey, field);
        return true;
    } catch (error) {
        logger.error('Redis DeleteHash Error:', error);
        return false;
    }
};

// Export the Redis client and all functions
module.exports = {
    client,
    set,
    get,
    del,
    exists,
    setHash,
    getHash,
    getAllHash,
    deleteHash
}; 