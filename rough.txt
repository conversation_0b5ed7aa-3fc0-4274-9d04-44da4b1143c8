/**
 * @swagger
 * /auth/login:
 *   post:
 *     summary: fetch color preferences
 *     tags:
 *       - Auth
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - loginKey: body
 *           in: body
 *           description: Connection details
 *           required: true
 *         - password: body
 *           in: body
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - loginKey
 *               - password
 *             properties:
 *               loginKey:
 *                 type: string
 *                 description: email or mobile_number
 *               password:
 *                 type: string
 *                 description: password
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


//role model

    roleDisplayName: {type: String, default: '' }, //always dynamic
    role: {type: String, default: '' }, //main role always by default
    rolegroup: { type: String, default: 'backend', enum: roleGroup },
    desc: {type: String, default: '' },
    isDeleted: {type: Boolean,default: false,enum: [true, false]},
    status: {type: String,default: "Active",enum: status},