const ffmpeg = require('fluent-ffmpeg');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');

class MediaProcessor {
    constructor() {
        // Ensure temp directory exists
        this.tempDir = path.join(__dirname, 'temp');
        this.ensureDirectories();
        
        // Initialize S3 client
        this.s3 = new S3Client({
            region: process.env.AWS_REGION,
            credentials: {
                accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
            }
        });
    }

    ensureDirectories() {
        if (!fs.existsSync(this.tempDir)) {
            fs.mkdirSync(this.tempDir, { recursive: true });
        }
    }

    // Convert time format (MM:SS or HH:MM:SS) to seconds
    timeToSeconds(timeStr) {
        if (!timeStr) return 0;
        
        const parts = timeStr.split(':').map(Number);
        if (parts.length === 2) {
            // MM:SS format
            return parts[0] * 60 + parts[1];
        } else if (parts.length === 3) {
            // HH:MM:SS format
            return parts[0] * 3600 + parts[1] * 60 + parts[2];
        }
        return 0;
    }

    // Convert seconds to time format
    secondsToTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    // Download file from URL
    async downloadFile(fileUrl, filename) {
        try {
            console.log(`Downloading file from: ${fileUrl}`);
            const response = await axios({
                method: 'GET',
                url: fileUrl,
                responseType: 'stream',
                timeout: 30000, // 30 seconds timeout
            });

            const filePath = path.join(this.tempDir, filename);
            const writer = fs.createWriteStream(filePath);

            return new Promise((resolve, reject) => {
                response.data.pipe(writer);
                writer.on('finish', () => {
                    console.log(`File downloaded successfully: ${filePath}`);
                    resolve(filePath);
                });
                writer.on('error', reject);
            });
        } catch (error) {
            console.error('Error downloading file:', error.message);
            throw new Error(`Failed to download file: ${error.message}`);
        }
    }

    // Get file duration using FFmpeg
    getFileDuration(filePath) {
        return new Promise((resolve, reject) => {
            ffmpeg.ffprobe(filePath, (err, metadata) => {
                if (err) {
                    reject(new Error(`Failed to get file duration: ${err.message}`));
                    return;
                }
                const duration = metadata.format.duration;
                resolve(duration);
            });
        });
    }

    // Upload file to S3 using presigned URL
    async uploadToS3(filePath, presignedUrl) {
        try {
            const fileStream = fs.createReadStream(filePath);
            const fileStats = fs.statSync(filePath);
            
            await axios.put(presignedUrl, fileStream, {
                headers: {
                    'Content-Type': 'application/octet-stream',
                    'Content-Length': fileStats.size
                },
                maxContentLength: Infinity,
                maxBodyLength: Infinity
            });
            return true;
        } catch (error) {
            console.error('Error uploading to S3:', error.message);
            if (error.response) {
                console.error('Response status:', error.response.status);
                console.error('Response data:', error.response.data);
            }
            throw new Error(`Failed to upload to S3: ${error.message}`);
        }
    }

    // Generate presigned URL for S3 upload
    async generatePresignedUrl(fileName, fileType) {
        const key = `uploads/${Date.now()}_${fileName}`;
        const command = new PutObjectCommand({
            Bucket: process.env.S3_BUCKET_NAME,
            Key: key,
            ContentType: fileType
        });

        const presignedUrl = await getSignedUrl(this.s3, command, { expiresIn: 3600 }); // 1 hour expiry
        const fileUrl = `https://d2p8o1syjdr4y0.cloudfront.net/${key}`;

        return {
            presignedUrl,
            fileUrl,
            key
        };
    }

    // Cut media file from start time to end time and upload to S3
    async cutMediaFile(fileUrl, startTime, endTime, outputFormat = 'mp3') {
        try {
            // Generate unique filename
            const inputFilename = `input_${uuidv4()}.mp4`;
            const outputFilename = `output_${uuidv4()}.${outputFormat}`;
            
            // Download the file
            const inputPath = await this.downloadFile(fileUrl, inputFilename);
            const tempOutputPath = path.join(this.tempDir, outputFilename);

            // Convert time strings to seconds
            const startSeconds = this.timeToSeconds(startTime);
            const endSeconds = this.timeToSeconds(endTime);

            // Get file duration to validate end time
            const fileDuration = await this.getFileDuration(inputPath);
            const actualEndSeconds = endSeconds > 0 ? Math.min(endSeconds, fileDuration) : fileDuration;

            console.log(`File duration: ${this.secondsToTime(fileDuration)}`);
            console.log(`Cutting from ${this.secondsToTime(startSeconds)} to ${this.secondsToTime(actualEndSeconds)}`);

            // Calculate duration for cutting
            const cutDuration = actualEndSeconds - startSeconds;

            // Generate presigned URL for S3 upload
            const { presignedUrl, fileUrl: s3FileUrl, key } = await this.generatePresignedUrl(outputFilename, `audio/${outputFormat}`);

            return new Promise((resolve, reject) => {
                let command = ffmpeg(inputPath)
                    .setStartTime(startSeconds)
                    .setDuration(cutDuration);

                // Set output format and codec based on extension
                if (outputFormat === 'mp3') {
                    command = command
                        .audioCodec('libmp3lame')
                        .audioBitrate(128);
                } else if (outputFormat === 'mp4') {
                    command = command
                        .videoCodec('copy')
                        .audioCodec('copy')
                        .outputOptions(['-avoid_negative_ts make_zero']);
                } else if (outputFormat === 'wav') {
                    command = command.audioCodec('pcm_s16le');
                } else if (outputFormat === 'aac') {
                    command = command.audioCodec('aac');
                }

                command
                    .output(tempOutputPath)
                    .on('start', (commandLine) => {
                        console.log('FFmpeg command:', commandLine);
                    })
                    .on('progress', (progress) => {
                        console.log(`Processing: ${progress.percent}% done`);
                    })
                    .on('end', async () => {
                        console.log('Media cutting completed successfully');
                        
                        try {
                            // Upload to S3
                            await this.uploadToS3(tempOutputPath, presignedUrl);
                            
                            // Clean up temporary files
                            fs.unlinkSync(inputPath);
                            fs.unlinkSync(tempOutputPath);

                            resolve({
                                success: true,
                                fileUrl: s3FileUrl,
                                key: key,
                                duration: cutDuration,
                                startTime: this.secondsToTime(startSeconds),
                                endTime: this.secondsToTime(actualEndSeconds)
                            });
                        } catch (uploadError) {
                            // Clean up temporary files
                            fs.unlinkSync(inputPath);
                            fs.unlinkSync(tempOutputPath);
                            reject(uploadError);
                        }
                    })
                    .on('error', (err) => {
                        console.error('FFmpeg error:', err.message);
                        
                        // Clean up input file
                        fs.unlinkSync(inputPath);
                        if (fs.existsSync(tempOutputPath)) {
                            fs.unlinkSync(tempOutputPath);
                        }
                        
                        reject(new Error(`Media processing failed: ${err.message}`));
                    })
                    .run();
            });

        } catch (error) {
            console.error('Error in cutMediaFile:', error.message);
            throw error;
        }
    }

    // Process JSON data with media cutting
    async processMediaFromJson(jsonData) {
        try {
            const { file_url, startTime, end_time, output_format = 'mp3' } = jsonData;

            if (!file_url) {
                throw new Error('file_url is required');
            }

            if (!startTime && !end_time) {
                throw new Error('At least startTime or end_time is required');
            }

            // Use default values if not provided
            const start = startTime || '00:00';
            const end = end_time || null;

            console.log('Processing media with parameters:');
            console.log(`URL: ${file_url}`);
            console.log(`Start Time: ${start}`);
            console.log(`End Time: ${end}`);
            console.log(`Output Format: ${output_format}`);

            const result = await this.cutMediaFile(file_url, start, end, output_format);
            return result;

        } catch (error) {
            console.error('Error processing media from JSON:', error.message);
            throw error;
        }
    }

    // Clean up temporary files
    cleanupTempFiles() {
        try {
            const files = fs.readdirSync(this.tempDir);
            files.forEach(file => {
                const filePath = path.join(this.tempDir, file);
                fs.unlinkSync(filePath);
                console.log(`Cleaned up: ${filePath}`);
            });
        } catch (error) {
            console.error('Error cleaning up temp files:', error.message);
        }
    }
}

module.exports = MediaProcessor; 