const { catchAsync } = require("../utils/catchAsync");
// const { group_chatService, tokenService } = require("../services");
// const { group_chatService } = require("../../../allservice")
// const group_chatService = require("../services/group_chat.service")
const httpStatus = require("http-status");



const handleRequest = (serviceFunction, reqQuery, reqFile, reqParam) => {
  return catchAsync(async (req, res) => {
    let user = req.user ? req.user : {};
    let requestField;
    
    if (reqQuery) {
      requestField = req.query;
    } else if (reqFile) {
      requestField = {
        files: req.files,
        body: req.body
      };
    } else if (reqParam) {
        Object.entries(req.params).forEach(([key , value])=>{
            if(key.includes("id") && _isAllNumbers(value)){
                req.body.filters[key] = Number(value)
            }
        })
      requestField = req.params;
    } else {
      if(req.body.filters){
        Object.entries(req.body.filters).forEach(([key , value])=>{
            if(key.includes("id") && _isAllNumbers(value)){
                req.body.filters[key] = Number(value)
            }
        })
      }
      requestField = req.body;
    }
    
    const result = await serviceFunction(requestField, user);
    res.status(httpStatus.OK).json({success: true, result});
  });
};

module.exports ={
    handleRequest
}