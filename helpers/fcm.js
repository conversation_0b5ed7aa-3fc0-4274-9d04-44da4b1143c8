const { admin } = require('../configuration/firebase-config');

// Subscribe a device to a topic
async function subscribeToTopic(token, topic) {
  try {
    const res = await admin.messaging().subscribeToTopic(token, topic);
    console.log(`Subscribed to ${topic}:`, res);
    return res;
  } catch (error) {
    console.error('Subscription error:', error);
    throw error;
  }
}

// Unsubscribe a device from a topic
async function unsubscribeFromTopic(token, topic) {
  try {
    const res = await admin.messaging().unsubscribeFromTopic(token, topic);
    console.log(`Unsubscribed from ${topic}:`, res);
    return res;
  } catch (error) {
    console.error('Unsubscription error:', error);
    throw error;
  }
}

// Send message to topic
async function sendToTopic(topic, title, body) {
  const message = {
    topic,
    notification: {
      title,
      body,
    },
    android: {
      priority: 'high',
    },
    apns: {
      headers: {
        'apns-priority': '10',
      },
    },
  };

  try {
    const res = await admin.messaging().send(message);
    console.log(`Message sent to topic "${topic}":`, res);
    return res;
  } catch (error) {
    console.error('Send error:', error);
    throw error;
  }
}

// Send push notification to many device tokens in batches (no data message, just notification)
async function sendPushToManyTokens(tokens, title, body) {
  const BATCH_SIZE = 500;
  const message = {
    notification: { title, body },
    android: { priority: 'high' },
    apns: { headers: { 'apns-priority': '10' } },
  };

  let results = [];
  for (let i = 0; i < tokens.length; i += BATCH_SIZE) {
    const batch = tokens.slice(i, i + BATCH_SIZE);
    const batchMessage = { ...message, tokens: batch };
    try {
      const res = await admin.messaging().sendMulticast(batchMessage);
      console.log(`Batch ${i / BATCH_SIZE + 1}:`, res);
      results.push(res);
    } catch (error) {
      console.error('Batch send error:', error);
      results.push({ error });
    }
  }
  return results;
}

module.exports = { subscribeToTopic, unsubscribeFromTopic, sendToTopic, sendPushToManyTokens }; 