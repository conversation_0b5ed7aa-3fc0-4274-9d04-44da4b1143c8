let io = null;

const initSocket = (serverIO) => {
    console.log("hereeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee")
  io = serverIO;

  io.on("connection", (socket) => {
    console.log("New client connected");

    // Join a specific room (e.g., user-specific or group-specific)
    socket.on("join", (userId) => {
      const room = `user_${userId}`;
      socket.join(room);
      console.log(`User ${userId} joined their room`);

      // ✅ Emit back to client (important for testing)
      socket.emit("joined", `User ${userId} joined their room`);
    });

    socket.on("join-group", (groupId) => {
      socket.join(`group_${groupId}`);
      console.log(`User joined group_${groupId}`);
    });

    socket.on("disconnect", () => {
      console.log("Client disconnected");
    });
  });
};

const getIO = () => {
  if (!io) throw new Error("Socket.io not initialized!");
  return io;
};

module.exports = {
  initSocket,
  getIO,
};
