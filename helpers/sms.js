// var unirest = require("unirest");

// const sendMobileVerificationsms = async (mobile_number, otp, hash) => {
    
//     var req = unirest(process.env.SMS_METHOD, process.env.SMS_URL);

//     req.headers({
//         authorization: process.env.SMS_API_KEY,
//     });

//     req.form({
//         sender_id: process.env.PHONE_VERIFICATION_SMS_SENDER_ID,
//         message: process.env.PHONE_VERIFICATION_SMS_MESSAGE_ID,
//         variables_values: `${otp}|${hash}`,
//         route: process.env.SMS_ROUTE,
//         numbers: `${mobile_number}`,
//     });

//     req.end(function (res) {
//         // console.log(res)
//     });
// };

// const sendUserResetPasswordsms = async (mobile_number, otp, hash) => {
//     //console.log("Called----- sms service----", mobile_number, otp);
//     var req = unirest(process.env.SMS_METHOD, process.env.SMS_URL);

//     req.headers({
//         authorization: process.env.SMS_API_KEY
//     });

//     req.form({
//         sender_id: process.env.SMS_SENDER_ID,
//         message: process.env.USER_RESETPASSWORD_SMS_MESSAGE_ID,
//         variables_values: `${otp}|${hash}`,
//         route: process.env.SMS_ROUTE,
//         numbers: `${mobile_number}`
//     });

//     req.end(function (res) {
//         //console.log("res-------- result", res);
//         // if (res.error) throw new Error(res.error);
//         // //console.log(res.body);
//     });
// };
const path = require("path")
require('dotenv').config({ path: path.join(__dirname, '../.env') });
const accountSid = process.env.PLATFROM === "test"? process.env.DEV_ACCOUNT_SID : process.env.PROD_ACCOUNT_SID

const authToken = process.env.PLATFROM === "test"? process.env.DEV_AUTH_TOKEN : process.env.PROD_AUTH_TOKEN
 
const client = require('twilio')(accountSid, authToken);

const sendSms = async (body , reciepentNumber) =>{

    try {
        reciepentNumber = reciepentNumber.split("-")[0]+reciepentNumber.split("-")[1]
        const sms = await client.messages
        .create({
            from: process.env.PLATFROM === "test"? process.env.DEV_NUMBER : process.env.PROD_NUMBER,
            to: reciepentNumber || '+91**********',
            body
        })
        console.log(sms)
        
    } catch (error) {
       console.log(error) 
    }
    

}

// sendSms("test message", "+91-**********")

   
module.exports ={
    // sendMobileVerificationsms,
    // sendUserResetPasswordsms,
    sendSms
}