{"name": "restfullapi", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node src/app.js", "dev": "nodemon src/app.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.758.0", "@aws-sdk/s3-request-presigner": "^3.758.0", "@mailchimp/mailchimp_transactional": "^1.0.59", "aws-sdk": "^2.1692.0", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "country-list": "^2.3.0", "csv-parser": "^3.0.0", "dotenv": "^16.0.0", "ejs": "^3.1.10", "email-templates": "^12.0.1", "express": "^4.17.1", "express-fileupload": "^1.2.1", "firebase-admin": "^13.2.0", "fluent-ffmpeg": "^2.1.2", "fs": "0.0.1-security", "gitignore": "^0.7.0", "googleapis": "^143.0.0", "http-status": "^1.5.0", "ioredis": "^5.3.2", "joi": "^17.6.0", "jsonwebtoken": "^8.5.1", "jwt-decode": "^4.0.0", "moment": "^2.29.1", "mongoose": "^5.11.11", "mongoose-aggregate-paginate-v2": "^1.1.2", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "nanoid": "^3.3.2", "neo4j-driver": "^5.28.1", "node-mandrill": "^1.0.1", "nodemon": "^2.0.22", "otp-generator": "^4.0.1", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "path": "^0.12.7", "pg": "^8.13.1", "pg-hstore": "^2.3.4", "sequelize": "^6.37.5", "sib-api-v3-sdk": "^8.5.0", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-model-validator": "^3.0.21", "swagger-ui-express": "^5.0.1", "twilio": "^5.4.5", "unirest": "^0.6.0", "util": "^0.12.4", "uuid": "^11.1.0", "uuidv4": "^6.2.13", "validator": "^13.5.2", "winston": "^3.11.0"}, "devDependencies": {"@types/express": "^4.17.13", "chai": "^5.2.0", "mocha": "^11.1.0", "socket.io-client": "^4.8.1"}}