const express = require("express");
const router = express.Router();
const validate = require("../../../helpers/validate");
const  familyValidation  = require("../validations/family.validations");
const familyController = require("../controllers/family.controller");
const multer = require('multer');
const { storage } = require("../../../configuration/storage");
const { authorize } = require("../../../authwires/auth");
const upload = multer({ storage: storage });


router.use(authorize)

/**
 * @swagger
 * /family/add-family:
 *   post:
 *     summary: add family
 *     tags:
 *       - Family
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               relationship_type:
 *                 type: string
 *                 description: Display name for the family
 *               user_id:
 *                 type: number
 *                 description : 2nd family
 *               family_member_id:
 *                 type: number
 *                 description: Family of the user
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/add-family', validate(familyValidation.addFamily), familyController.addFamily)
/**
 * @swagger
 * /family/edit-family:
 *   post:
 *     summary: edit family
 *     tags:
 *       - Family
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               family_id:
 *                 type: number
 *               user_id:
 *                 type: number
 *                 description : 2nd family
 *               family_member_id:
 *                 type: number
 *                 description: Display name for the family
 *               relationship_type:
 *                 type: string
 *                 description: Family of the user
 *               is_deleted:
 *                 type: boolean
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/edit-family', validate(familyValidation.editFamily), familyController.editFamily)



// authorize routes
// router.use(authorize)



/**
 * @swagger
 * /family/get-familys:
 *   post:
 *     summary: fetch color preferences
 *     tags:
 *       - Family
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               filters:
 *                 type: object
 *                 description: family name
 *               page:
 *                type: number
 *               limit:
 *                type: number
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/get-familys', validate(familyValidation.getFamilys), familyController.getFamilys)
// router.post('/get-family', validate(familyValidation.getFamily), familyController.getFamily)
// router.post('/delete-family', validate(familyValidation.deleteFamily), familyController.deleteFamily)


module.exports = router;
