'use strict';

module.exports = (sequelize, DataTypes) => {
  const Family = sequelize.define('Family', {
    user_id:{
      type: DataTypes.INTEGER
    },
    family_member_id:{
      type: DataTypes.INTEGER
    },
    relationship_type:{
      type: DataTypes.STRING
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    created_at:{
      type: DataTypes.DATE
    },
    updated_at:{
      type: DataTypes.DATE
    },
  }, {
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });

  Family.associate = (models) => {
    Family.belongsTo(models.User, { foreignKey: 'user_id', as: 'User1' });
    Family.belongsTo(models.User, { foreignKey: 'family_member_id', as: 'User2' });
  };
  return Family;
};