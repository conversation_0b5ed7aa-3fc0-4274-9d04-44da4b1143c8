const Joi = require("joi");

module.exports.addFamily = {
    body: Joi.object().keys({
        relationship_type: Joi.string().required(),
        user_id: Joi.number().required(),
        family_member_id: Joi.number().required()
    }),
};
module.exports.editFamily = {
    body: Joi.object().keys({
        family_id: Joi.number().required(),
        relationship_type: Joi.string().optional().allow(null, ""),
        user_id: Joi.number().optional().allow(null , ""),
        family_member_id: Joi.number().optional().allow(null , ""),
        is_deleted: Joi.boolean().optional().allow(null, "")
    }),
};
module.exports.getFamilys = {
    body: Joi.object().keys({
        filters: Joi.object().required(),
        page: Joi.number().required(),
        limit: Joi.number().required()
    }),
};