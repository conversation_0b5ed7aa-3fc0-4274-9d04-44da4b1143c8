const { catchAsync } = require("../../../utils/catchAsync");
// const { familyService, tokenService } = require("../services");
// const { familyService } = require("../../../allservice")
const familyService = require("../services/family.service")
const httpStatus = require("http-status");



const {handleRequest} = require("../../../helpers/handleRequest")
  module.exports.addFamily = handleRequest(familyService.addFamily);
  module.exports.editFamily = handleRequest(familyService.editFamily);
  module.exports.getFamilys = handleRequest(familyService.getFamilys);
 