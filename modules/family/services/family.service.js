const {Family, Follower , User} = require("../../../models")
const httpStatus = require("http-status");
const ApiError = require("../../../utils/ApiError");
// const activityService = require("../../activity/services/activity.service");
// const { _isObjectId } = require("../../../helpers/global.functions");
// const  mongoose = require("mongoose");

const addFamily = async(reqBody) =>{
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }
 const family = await Family.create(reqBody)
 if(reqBody.createdBy){
    // await activityService.addActivity({actionOn: reqBody.createdBy, actionBy: reqBody.createdBy, actionName: "addFamily" , actionDescription: `family name ${reqBody.familyDisplayName}` })
 }
 return family
}
const editFamily = async(reqBody) =>{
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }
    const [updatedCount, updatedFamilys] = await Family.update(reqBody, {
        where: { id: reqBody.family_id },
        returning: true,
    });

    if (updatedCount === 0) {
        throw new ApiError(httpStatus.NOT_FOUND, "Family not found");
    }
    return updatedFamilys[0];
}

const getFamilys = async ({ filters, page, limit }, user) => {
    // console.log(user);

    // Clean and transform filters
    Object.entries(filters).forEach(([key, value]) => {
        if (value === "") delete filters[key];
    });

    // Define pagination options
    const offset = (page - 1) * limit;

    // Fetch familys using Sequelize
    const familyDocs = await Family.findAndCountAll({
        include: [
            {
                model: User,
                as: 'User1',
                attributes: ['id', 'username', 'first_name', 'last_name', 'profile_picture_url']
            },
            {
                model: User,
                as: 'User2',
                attributes: ['id', 'username', 'first_name', 'last_name', 'profile_picture_url']
            }
        ],
        where: filters,
        attributes: ["user_id", "family_member_id", "relationship_type", "created_at"],
        limit,
        offset,
    });

    return {
        total: familyDocs.count,
        familys: familyDocs.rows,
        page,
        limit
    };
};

module.exports={
    addFamily,
    getFamilys,
    editFamily
}