'use strict';

module.exports = (sequelize, DataTypes) => {
  const Group = sequelize.define('Group', {
    group_name:{
      type: DataTypes.STRING
    },
    description:{
      type: DataTypes.STRING
    },
    group_image:{
      type: DataTypes.STRING
    },
    group_cover:{
      type: DataTypes.STRING
    },
    created_by:{
      type: DataTypes.INTEGER
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    created_at:{
      type: DataTypes.DATE
    },
    is_family_group:{
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    updated_at:{
      type: DataTypes.DATE
    },
  }, {
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });

  Group.associate = (models) => {
    Group.hasMany(models.Group_member, { 
      foreignKey: 'group_id', 
      as: 'members',
      onDelete: 'CASCADE'
    });
    Group.hasMany(models.Reportgroup, { 
      foreignKey: 'group_id', 
      as: 'reports',
      onDelete: 'CASCADE'
    });
  };

  // Group.associate = (models) => {
  //   Group.hasMany(models.User, { foreignKey: 'group', as: 'users' });
  // };
  return Group;
};