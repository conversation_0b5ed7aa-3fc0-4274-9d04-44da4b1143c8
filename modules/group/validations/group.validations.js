const Joi = require("joi");

module.exports.addGroup = {
    body: Joi.object().keys({
        group_name: Joi.string().required(),
        description: Joi.string().optional().allow(null , ""),
        group_members: Joi.array().items(Joi.number()).required(),
        group_image: Joi.string().optional().allow(null, ""),
        group_cover: Joi.string().optional().allow(null, ""),
        is_family_group: Joi.boolean()
    }),
};
module.exports.editGroup = {
    body: Joi.object().keys({
        group_id: Joi.number().required(),
        group_name: Joi.string().optional().allow(null, ""),
        description: Joi.string().optional().allow(null, ""),
        group_image: Joi.string().optional().allow(null, ""),
        group_cover: Joi.string().optional().allow(null, ""),
        created_by: Joi.number().optional().allow(null , ""),
        is_deleted: Joi.boolean().optional().allow(null, ""),
        group_members: Joi.array().items(Joi.number()),
        is_family_group: Joi.boolean()
    }),
};
module.exports.getGroups = {
    body: Joi.object().keys({
        filters: Joi.object().required(),
        page: Joi.number().required(),
        limit: Joi.number().required()
    }),
};

module.exports.updateMemberStatus = {
    body: Joi.object().keys({
        group_id: Joi.number().required(),
        user_id: Joi.number().required(),
        status: Joi.string().valid('pending', 'accepted', 'rejected').required()
    })
};