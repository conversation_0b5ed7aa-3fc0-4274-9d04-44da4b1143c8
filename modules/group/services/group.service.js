const {Group, Follower, Group_member , User} = require("../../../models")
const Friend = require("../../friend/models/friend.model");
const httpStatus = require("http-status");
const ApiError = require("../../../utils/ApiError");
const { Sequelize , Op } = require('sequelize');
// const User = require("../../../user/models/user.model");

// const User = require("../../user/models/user.model");
// const activityService = require("../../activity/services/activity.service");
// const { _isObjectId } = require("../../../helpers/global.functions");
// const  mongoose = require("mongoose");

const addGroup = async(reqBody, user) => {
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }

    reqBody["created_by"] = user.id;
    // Create the group
    const group = await Group.create(reqBody)

    let groupMembers = []
    groupMembers.push({
        group_id: group.id,
        user_id: reqBody.created_by,
        role: 'admin',
        is_deleted: false,
        status: 'accepted' // Creator is automatically accepted
    });
    // Add group members
    if (reqBody.group_members && reqBody.group_members.length > 0) {
        reqBody.group_members.map((memberId) => {
            groupMembers.push({

                group_id: group.id,
                user_id: memberId,
                role: 'member',
                is_deleted: false,
                status: 'pending' 
            })
        });
    }
    await Group_member.bulkCreate(groupMembers);

    if(reqBody.createdBy){
        // await activityService.addActivity({actionOn: reqBody.createdBy, actionBy: reqBody.createdBy, actionName: "addGroup" , actionDescription: `group name ${reqBody.groupDisplayName}` })
    }
    return group
}

const editGroup = async(reqBody) => {
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }

    const groupId = reqBody.group_id;
    delete reqBody.group_id; // Remove group_id from update data

    if(reqBody.group_members) delete reqBody.group_members
    // Update group details
    const [updatedCount, updatedGroups] = await Group.update(reqBody, {
        where: { id: groupId },
        returning: true,
    });

    if (updatedCount === 0) {
        throw new ApiError(httpStatus.NOT_FOUND, "Group not found");
    }

    // Handle group members update if provided
    if (reqBody.group_members) {
        // Get existing members
        const existingMembers = await Group_member.findAll({
            where: { group_id: groupId }
        });

        // Delete members that are not in the new list
        const newMemberIds = new Set(reqBody.group_members);
        await Group_member.destroy({
            where: {
                group_id: groupId,
                user_id: {
                    [Op.notIn]: Array.from(newMemberIds)
                }
            }
        });

        // Add new members with pending status
        const membersToAdd = reqBody.group_members
            .filter(memberId => !existingMembers.some(m => m.user_id === memberId))
            .map(memberId => ({
                group_id: groupId,
                user_id: memberId,
                is_deleted: false,
                status: 'pending'
            }));

        if (membersToAdd.length > 0) {
            await Group_member.bulkCreate(membersToAdd);
        }
    }

    return updatedGroups[0];
}

const updateMemberStatus = async(reqBody) => {
    const { group_id, user_id, status } = reqBody;

    // Find the group member
    const groupMember = await Group_member.findOne({
        where: {
            group_id,
            user_id,
            is_deleted: false
        }
    });

    if (!groupMember) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Group member not found');
    }

    // Update the status
    await groupMember.update({ status });

    return {
        message: 'Member status updated successfully',
        data: groupMember
    };
}

const getGroups = async ({ filters, page, limit }, user) => {
    // Clean and transform filters
    Object.entries(filters).forEach(([key, value]) => {
        if (value === "") delete filters[key];
    });

    let searchCondition = {};
    if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        searchCondition = {
            [Op.or]: [
                Sequelize.literal(`LOWER("Group"."group_name") LIKE '%${searchTerm}%'`)
            ]
        };
    }


    // Define pagination options
    const offset = (page - 1) * limit;

    let whereClause = { ...filters };
    let includeMembers = [
        {
            model: Group_member,
            as: 'members',
            include: [
                {
                    model: User,
                    as: 'user',
                    attributes: ['id', 'username', 'first_name', 'last_name', 'profile_picture_url']
                }
            ]
        }
    ];

    if(filters.myAcceptedGroup){
        delete whereClause.myAcceptedGroup;
        includeMembers = [
            {
                model: Group_member,
                as: 'members',
                where: {
                    user_id: user.id,
                    status: "accepted",
                    is_deleted: false
                },
                required: true,
                include: [
                    {
                        model: User,
                        as: 'user',
                        attributes: ['id', 'username', 'first_name', 'last_name', 'profile_picture_url']
                    }
                ]
            }
        ];
    }
    if(filters.myPendingGroup){
        delete whereClause.myPendingGroup;
        includeMembers = [
            {
                model: Group_member,
                as: 'members',
                where: {
                    user_id: user.id,
                    status: "pending",
                    is_deleted: false
                },
                required: true,
                include: [
                    {
                        model: User,
                        as: 'user',
                        attributes: ['id', 'username', 'first_name', 'last_name', 'profile_picture_url']
                    }
                ]
            }
        ];
    }
    // Handle myGroup filter
    if (filters.myGroup) {
        delete whereClause.myGroup;
        includeMembers = [
            {
                model: Group_member,
                as: 'members',
                where: {
                    user_id: user.id,
                    is_deleted: false
                },
                required: true,
                include: [
                    {
                        model: User,
                        as: 'user',
                        attributes: ['id', 'username', 'first_name', 'last_name', 'profile_picture_url']
                    }
                ]
            }
        ];
    }

    if(filters.search || whereClause.search){
        delete filters.search;
        delete whereClause.search;
        whereClause = {
            [Op.and]: [
                whereClause,
                searchCondition
            ]
        }
    }else{
        whereClause = {
            [Op.and]: [
                whereClause
            ]
        }
    }
    // Fetch groups using Sequelize
    const groupDocs = await Group.findAndCountAll({
        where: whereClause,
        include: includeMembers,
        attributes: {
            include: [
                [
                    Sequelize.literal(`(
                        SELECT COUNT(*) 
                        FROM "Group_members" AS gm 
                        WHERE gm.group_id = "Group".id 
                        AND gm.status = 'pending'
                    )`),
                    "pendingMembersCount"
                ],
                [
                    Sequelize.literal(`(
                        SELECT COUNT(*) 
                        FROM "Reportgroups" AS rg 
                        WHERE rg.group_id = "Group".id
                    )`),
                    "totalReports"
                ],
                [
                    Sequelize.literal(`(
                        SELECT COUNT(*) 
                        FROM "Group_members" AS gm 
                        WHERE gm.group_id = "Group".id 
                        AND gm.status = 'accepted'
                        AND gm.role = 'admin'
                    )`),
                    "adminCounts"
                ],
                [
                    Sequelize.literal(`(
                        SELECT status 
                        FROM "Group_members" AS gm 
                        WHERE gm.group_id = "Group".id 
                        AND gm.user_id = ${user.id}
                        AND gm.is_deleted = false
                    )`),
                    'myMembershipStatus'
                ],
                [
                    Sequelize.literal(`(
                        SELECT role 
                        FROM "Group_members" AS gm 
                        WHERE gm.group_id = "Group".id 
                        AND gm.user_id = ${user.id}
                        AND gm.is_deleted = false
                    )`),
                    'myRole'
                ],
                [
                    Sequelize.literal(`(
                        SELECT COUNT(*) 
                        FROM "Group_members" AS gm 
                        WHERE gm.group_id = "Group".id 
                        AND gm.status = 'accepted'
                    )`),
                    "acceptedMembersCount"
                ],
                [
                    Sequelize.literal(`(
                        SELECT COUNT(*) 
                        FROM "Group_members" AS gm 
                        WHERE gm.group_id = "Group".id 
                        AND gm.status = 'rejected'
                    )`),
                    "rejectedMembersCount"
                ]
            ]
        },
        limit,
        offset,
    });

    // Transform the response to include member details and user's status
    const transformedGroups = await Promise.all(groupDocs.rows.map(async group => {
        const groupData = group.toJSON();
        let userStatus = null;
        let userRole = null;

        // Find user's status and role in the group
        if (groupData.members) {
            const userMember = groupData.members.find(member => member.user_id === user.id);
            if (userMember) {
                userStatus = userMember.status;
                userRole = userMember.role;
            }
        }

        // Get friend statuses for all members using Neo4j
        const membersWithFriendStatus = await Promise.all(groupData.members?.map(async member => {
            const myStatus = await Friend.getFriendshipStatus(user.id, member.user_id);
            const friendStatus = await Friend.getFriendshipStatus(member.user_id, user.id);
            const followerCount = await Friend.getFollowersCount(member.user_id);

            return {
                id: member.id,
                user_id: member.user_id,
                status: member.status,
                role: member.role,
                user: {
                    id: member.user.id,
                    username: member.user.username,
                    first_name: member.user.first_name,
                    last_name: member.user.last_name,
                    profile_picture_url: member.user.profile_picture_url,
                    myStatus: myStatus?.status || null,
                    friendStatus: friendStatus?.status || null,
                    followersCount: followerCount
                }
            };
        }) || []);

        return {
            ...groupData,
            members: membersWithFriendStatus,
            memberCounts: {
                pending: groupData.pendingMembersCount,
                accepted: groupData.acceptedMembersCount,
                rejected: groupData.rejectedMembersCount
            },
            myStatus: userStatus,
            myRole: userRole
        };
    }));

    return {
        total: groupDocs.count,
        groups: transformedGroups,
        page,
        limit
    };
};

const getGroup = async ({id}, user) => {
    id = Number(id);
    
    // Get group with member counts
    const group = await Group.findOne({
        where: { id, is_deleted: false },
        attributes: {
            include: [
                [
                    Sequelize.literal(`(
                        SELECT COUNT(*) 
                        FROM "Group_members" AS gm 
                        WHERE gm.group_id = "Group".id 
                        AND gm.status = 'pending'
                    )`),
                    "pendingMembersCount"
                ],
                [
                    Sequelize.literal(`(
                        SELECT COUNT(*) 
                        FROM "Reportgroups" AS rg 
                        WHERE rg.group_id = "Group".id
                    )`),
                    "totalReports"
                ],
                [
                    Sequelize.literal(`(
                        SELECT COUNT(*) 
                        FROM "Group_members" AS gm 
                        WHERE gm.group_id = "Group".id 
                        AND gm.status = 'accepted'
                        AND gm.role = 'admin'
                    )`),
                    "adminCounts"
                ],
                [
                    Sequelize.literal(`(
                        SELECT status 
                        FROM "Group_members" AS gm 
                        WHERE gm.group_id = "Group".id 
                        AND gm.user_id = ${user.id}
                        AND gm.is_deleted = false
                    )`),
                    'myMembershipStatus'
                ],
                [
                    Sequelize.literal(`(
                        SELECT role 
                        FROM "Group_members" AS gm 
                        WHERE gm.group_id = "Group".id 
                        AND gm.user_id = ${user.id}
                        AND gm.is_deleted = false
                    )`),
                    'myRole'
                ],
                [
                    Sequelize.literal(`(
                        SELECT COUNT(*) 
                        FROM "Group_members" AS gm 
                        WHERE gm.group_id = "Group".id 
                        AND gm.status = 'accepted'
                    )`),
                    "acceptedMembersCount"
                ],
                [
                    Sequelize.literal(`(
                        SELECT COUNT(*) 
                        FROM "Group_members" AS gm 
                        WHERE gm.group_id = "Group".id 
                        AND gm.status = 'rejected'
                    )`),
                    "rejectedMembersCount"
                ]
            ]
        },
        include: [{
            model: Group_member,
            as: 'members',
            where: { is_deleted: false },
            required: false,
            include: [{
                model: User,
                as: 'user',
                attributes: [
                    'id',
                    'username',
                    'first_name',
                    'last_name',
                    'email',
                    'profile_picture_url'
                ]
            }],
            attributes: [
                'id',
                'user_id',
                'role',
                'status'
            ]
        }]
    });

    if (!group) {
        throw new ApiError(httpStatus.NOT_FOUND, "Group not found");
    }

    // Transform the response to match getGroups structure
    const groupData = group.toJSON();
    let userStatus = null;
    let userRole = null;

    // Find user's status and role in the group
    if (groupData.members) {
        const userMember = groupData.members.find(member => member.user_id === user.id);
        if (userMember) {
            userStatus = userMember.status;
            userRole = userMember.role;
        }
    }

    // Get friend statuses for all members using Neo4j
    const membersWithFriendStatus = await Promise.all(groupData.members?.map(async member => {
        const myStatus = await Friend.getFriendshipStatus(user.id, member.user_id);
        const friendStatus = await Friend.getFriendshipStatus(member.user_id, user.id);
        const followerCount = await Friend.getFollowersCount(member.user_id);

        return {
            id: member.id,
            user_id: member.user_id,
            status: member.status,
            role: member.role,
            user: {
                id: member.user.id,
                username: member.user.username,
                first_name: member.user.first_name,
                last_name: member.user.last_name,
                profile_picture_url: member.user.profile_picture_url,
                myStatus: myStatus?.status || null,
                friendStatus: friendStatus?.status || null,
                followersCount: followerCount
            }
        };
    }) || []);

    const transformedGroup = {
        ...groupData,
        members: membersWithFriendStatus,
        memberCounts: {
            pending: groupData.pendingMembersCount,
            accepted: groupData.acceptedMembersCount,
            rejected: groupData.rejectedMembersCount
        },
        myStatus: userStatus,
        myRole: userRole
    };

    return transformedGroup;
};

module.exports={
    addGroup,
    getGroups,
    editGroup,
    updateMemberStatus,
    getGroup
}