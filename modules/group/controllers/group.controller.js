const { catchAsync } = require("../../../utils/catchAsync");
// const { groupService, tokenService } = require("../services");
// const { groupService } = require("../../../allservice")
const groupService = require("../services/group.service")
const httpStatus = require("http-status");
const ApiError = require('../../../utils/ApiError');
const { Group_member } = require('../../../models');

const handleRequest = (serviceFunction, reqQuery , reqFile, reqParam) => {
    return catchAsync(async (req, res) => {
      let user = req.user? req.user : {}
      const requestField = reqQuery?req.query:reqFile?{file:req.file,body:req.body}:reqParam?req.params:req.body
      const result = await serviceFunction(requestField, user);
      res.status(httpStatus.OK).json({success: true,result});
    });
};

module.exports.addGroup = handleRequest(groupService.addGroup);
module.exports.editGroup = handleRequest(groupService.editGroup);
module.exports.getGroups = handleRequest(groupService.getGroups);
module.exports.getGroup = handleRequest(groupService.getGroup , true);
module.exports.updateMemberStatus = handleRequest(groupService.updateMemberStatus);

 
 