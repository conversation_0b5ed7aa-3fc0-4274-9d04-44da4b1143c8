const express = require("express");
const router = express.Router();
const validate = require("../../../helpers/validate");
const  groupValidation  = require("../validations/group.validations");
const groupController = require("../controllers/group.controller");
const multer = require('multer');
const { storage } = require("../../../configuration/storage");
const { authorize } = require("../../../authwires/auth");
const upload = multer({ storage: storage });


router.use(authorize)

/**
 * @swagger
 * /group/add-group:
 *   post:
 *     summary: add group
 *     tags:
 *       - Group
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - group_name
 *               - description
 *               - created_by
 *               - group_members
 *             properties:
 *               group_name:
 *                 type: string
 *                 description: Display name for the group
 *               group_image:
 *                 type: string
 *                 description: Image of the group
 *               group_cover:
 *                 type: string
 *                 description: Cover image of the group
 *               description:
 *                 type: string
 *                 description: Description of the group
 *               created_by:
 *                 type: number
 *                 description: ID of the user creating the group
 *               group_members:
 *                 type: array
 *                 description: Array of user IDs to be added as group members
 *                 items:
 *                   type: number
 *               is_family_group:
 *                 type: boolean
 *     responses:
 *        200:
 *          description: Group created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/add-group', validate(groupValidation.addGroup), groupController.addGroup)
/**
 * @swagger
 * /group/edit-group:
 *   post:
 *     summary: edit group
 *     tags:
 *       - Group
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - group_id
 *             properties:
 *               group_id:
 *                 type: number
 *                 description: ID of the group to edit
 *               group_image:
 *                 type: string
 *                 description: Image of the group
 *               group_cover:
 *                 type: string
 *                 description: Cover image of the group
 *               group_name:
 *                 type: string
 *                 description: New name for the group
 *               description:
 *                 type: string
 *                 description: New description for the group
 *               is_deleted:
 *                 type: boolean
 *                 description: Whether the group is deleted
 *               is_family_group:
 *                 type: boolean
 *     responses:
 *        200:
 *          description: Group updated successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/edit-group', validate(groupValidation.editGroup), groupController.editGroup)

/**
 * @swagger
 * /group/update-member-status:
 *   post:
 *     summary: Update group member status
 *     tags:
 *       - Group
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Member status update details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - group_id
 *               - user_id
 *               - status
 *             properties:
 *               group_id:
 *                 type: number
 *                 description: ID of the group
 *               user_id:
 *                 type: number
 *                 description: ID of the user whose status is being updated
 *               status:
 *                 type: string
 *                 enum: [pending, accepted, rejected]
 *                 description: New status for the group member
 *     responses:
 *        200:
 *          description: Member status updated successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/update-member-status', validate(groupValidation.updateMemberStatus), groupController.updateMemberStatus)

// authorize routes
// router.use(authorize)



/**
 * @swagger
 * /group/get-groups:
 *   post:
 *     summary: fetch color preferences
 *     tags:
 *       - Group
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               filters:
 *                 type: object
 *                 properties:
 *                   is_family_group:
 *                    type: boolean
 *                    description: for get family send true else send false rather than evcerything remain same as group  
 *               page:
 *                type: number
 *               limit:
 *                type: number
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/get-groups', validate(groupValidation.getGroups), groupController.getGroups)


/**
 * @swagger
 * /group/get-group-by-id:
 *   get:
 *     summary: Profile Details
 *     tags:
 *       - Group
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: id
 *         in: query
 *         description: The type of the file
 *         required: false
 *         schema:
 *           type: string
 *     responses:
 *        200:
 *          description: Profile Details Fetched Successfully
 *        400:
 *          description: Bad Request
 */

router.get('/get-group-by-id', groupController.getGroup)
// router.post('/get-group', validate(groupValidation.getGroup), groupController.getGroup)
// router.post('/delete-group', validate(groupValidation.deleteGroup), groupController.deleteGroup)

// /**
//  * @swagger
//  * /group/user-group-media:
//  *   get:
//  *     summary: Profile Details
//  *     tags:
//  *       - Group
//  *     security:
//  *       - Token: []
//  *     produces:
//  *       - application/json
//  *     parameters:
//  *       - name: id
//  *         in: query
//  *         description: The type of the file
//  *         required: false
//  *         schema:
//  *           type: string
//  *     responses:
//  *        200:
//  *          description: Profile Details Fetched Successfully
//  *        400:
//  *          description: Bad Request
//  */

// router.get("/user-group-media", groupController.getUserGroupMedia)


module.exports = router;
