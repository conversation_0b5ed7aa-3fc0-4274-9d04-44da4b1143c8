const express = require("express");
const router = express.Router();
const validate = require("../../../helpers/validate");
const  donatorValidation  = require("../validations/donator.validations");
const donatorController = require("../controllers/donator.controller");
const multer = require('multer');
const { storage } = require("../../../configuration/storage");
const { authorize } = require("../../../authwires/auth");
const upload = multer({ storage: storage });


router.use(authorize)

/**
 * @swagger
 * /donator/add-donator:
 *   post:
 *     summary: add donator
 *     tags:
 *       - Donator
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               post_id:
 *                 type: number
 *                 description: Display name for the donator
 *               comment_id:
 *                 type: number
 *               user_id:
 *                 type: number
 *                 description: Donator of the user
 *               donatedAmount:
 *                 type: string
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/add-donator', validate(donatorValidation.addDonator), donatorController.addDonator)
/**
 * @swagger
 * /donator/edit-donator:
 *   post:
 *     summary: edit donator
 *     tags:
 *       - Donator
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               donator_id:
 *                 type: number
 *               user_id:
 *                 type: number
 *                 description: Display name for the donator
 *               post_id:
 *                 type: number
 *                 description: Donator of the user
 *               is_deleted:
 *                 type: boolean
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/edit-donator', validate(donatorValidation.editDonator), donatorController.editDonator)



// authorize routes
// router.use(authorize)



/**
 * @swagger
 * /donator/get-donators:
 *   post:
 *     summary: fetch color preferences
 *     tags:
 *       - Donator
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               filters:
 *                 type: object
 *                 description: donator name
 *               page:
 *                type: number
 *               limit:
 *                type: number
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/get-donators', validate(donatorValidation.getDonators), donatorController.getDonators)
// router.post('/get-donator', validate(donatorValidation.getDonator), donatorController.getDonator)
// router.post('/delete-donator', validate(donatorValidation.deleteDonator), donatorController.deleteDonator)


module.exports = router;
