const {Donator, Follower, User, Post} = require("../../../models")
const Friend = require("../../friend/models/friend.model");
const httpStatus = require("http-status");
const ApiError = require("../../../utils/ApiError");
const { Sequelize } = require("sequelize");
const { client: redisClient, set: redisSet, get: redisGet, setHash: redisSetHash, getHash: redisGetHash, getAllHash: redisGetAllHash } = require("../../../services/redis.service");
const logger = require("../../../utils/logger");
// const User = require("../../user/models/user.model");
// const activityService = require("../../activity/services/activity.service");
// const { _isObjectId } = require("../../../helpers/global.functions");
// const  mongoose = require("mongoose");

// Redis key generators
const getPostKey = (postId) => `post:${postId}`;
const getPostsListKey = (userId) => `posts:${userId}`;

// Helper function to update post cache with new donator count and total amount
const updatePostCache = async (postId, userId, isDonatord, donatedAmount) => {
    try {
        // Update individual post cache
        const postKey = getPostKey(postId);
        const cachedPost = await redisGet(postKey);
        
        if (cachedPost) {
            cachedPost.donatorCount = isDonatord ? 
                (parseInt(cachedPost.donatorCount) + 1) : 
                (parseInt(cachedPost.donatorCount) - 1);
            cachedPost.isDonatord = isDonatord;
            
            // Update total donated amount
            if (isDonatord) {
                cachedPost.totalDonatedAmount = (parseFloat(cachedPost.totalDonatedAmount || 0) + parseFloat(donatedAmount || 0)).toString();
            } else {
                cachedPost.totalDonatedAmount = (parseFloat(cachedPost.totalDonatedAmount || 0) - parseFloat(donatedAmount || 0)).toString();
            }
            
            await redisSet(postKey, cachedPost);
            logger.info(`Updated post cache for post ${postId} with new donator count: ${cachedPost.donatorCount} and total amount: ${cachedPost.totalDonatedAmount}`);
        }

        // Update post in all users' lists
        const allKeys = await redisClient.keys('posts:*');
        for (const key of allKeys) {
            if (key.includes(':count:')) continue; // Skip count keys
            
            const existingPosts = await redisGetAllHash(key) || {};
            if (existingPosts[postId]) {
                const post = existingPosts[postId];
                post.donatorCount = isDonatord ? 
                    (parseInt(post.donatorCount) + 1) : 
                    (parseInt(post.donatorCount) - 1);
                post.isDonatord = isDonatord;
                
                // Update total donated amount in lists
                if (isDonatord) {
                    post.totalDonatedAmount = (parseFloat(post.totalDonatedAmount || 0) + parseFloat(donatedAmount || 0)).toString();
                } else {
                    post.totalDonatedAmount = (parseFloat(post.totalDonatedAmount || 0) - parseFloat(donatedAmount || 0)).toString();
                }
                
                await redisSetHash(key, postId, post);
                logger.info(`Updated post in list ${key} with new donator count: ${post.donatorCount} and total amount: ${post.totalDonatedAmount}`);
            }
        }
    } catch (error) {
        logger.error(`Error updating post cache for post ${postId}:`, error);
    }
};

const addDonator = async(reqBody, user) => {
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }
    reqBody["user_id"] = user.id

    // Validate donatedAmount
    if (!reqBody.donatedAmount) {
        throw new ApiError(httpStatus.BAD_REQUEST, "Donation amount is required");
    }

    let donator = await Donator.findOne({
        where: {
            post_id: reqBody.post_id,
            user_id: user.id,
            is_deleted: false
        }
    });

    if(donator) {
        await donator.update({is_deleted: true})
        // Update cache - donator removed
        await updatePostCache(reqBody.post_id, user.id, false, donator.donatedAmount);
    } else {
        donator = await Donator.create(reqBody)
        // Update cache - donator added
        await updatePostCache(reqBody.post_id, user.id, true, reqBody.donatedAmount);
    }
    if(reqBody.createdBy){
       // await activityService.addActivity({actionOn: reqBody.createdBy, actionBy: reqBody.createdBy, actionName: "addDonator" , actionDescription: `donator name ${reqBody.donatorDisplayName}` })
    }
    return donator
}

const editDonator = async(reqBody, user) => {
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }
    
    let query = {}
    if(reqBody.post_id){
        query = {
            post_id: reqBody.post_id,
            user_id: user.id,
            is_deleted: false
        }
    } else {
        query = {id: reqBody.donator_id}
    }

    // Get existing donator to track amount changes
    const existingDonator = await Donator.findOne({ where: query });
    if (!existingDonator) {
        throw new ApiError(httpStatus.NOT_FOUND, "Donator not found");
    }

    const [updatedCount, updatedDonators] = await Donator.update(reqBody, {
        where: query,
        returning: true,
    });

    if (updatedCount === 0) {
        throw new ApiError(httpStatus.NOT_FOUND, "Donator not found");
    }

    // Update cache if post_id is provided
    if (reqBody.post_id) {
        const isDonatord = !reqBody.is_deleted;
        // If amount changed, update cache with the difference
        const amountDiff = reqBody.donatedAmount ? 
            (parseFloat(reqBody.donatedAmount) - parseFloat(existingDonator.donatedAmount)) : 0;
        await updatePostCache(reqBody.post_id, user.id, isDonatord, amountDiff.toString());
    }

    return updatedDonators[0];
}

const getDonators = async ({ filters, page, limit }, user) => {
    // Clean and transform filters
    Object.entries(filters).forEach(([key, value]) => {
        if (value === "") delete filters[key];
    });

    // Define pagination options
    const offset = (page - 1) * limit;

    filters = {
        ...filters,
        is_deleted: false
    }

    if(!filters.comment_id) filters = {
        ...filters,
        comment_id: null
    }

    // Fetch donators using Sequelize
    const donatorDocs = await Donator.findAndCountAll({
        include: [
            {
                model: User,
                as: 'user',
                attributes: [
                    "id", 
                    "username", 
                    "email", 
                    "profile_picture_url",
                    "first_name",
                    "last_name",
                    "date_of_birth",
                    "mobile_number"
                ]
            }
        ],
        where: filters,
        attributes: ["id", "post_id", "user_id", "donatedAmount", "created_at"],
        limit,
        offset,
    });

    // Get friend statuses for all donators using Neo4j
    const donatorsWithFriendStatus = await Promise.all(donatorDocs.rows.map(async (donator) => {
        const user = donator.user;
        
        // Get friend status using Neo4j
        const myStatus = await Friend.getFriendshipStatus(user.id, user.id);
        const friendStatus = await Friend.getFriendshipStatus(user.id, user.id);
        const followers = await Friend.getFollowers(user.id);
        const followerCount = followers.length;

        return {
            ...donator.toJSON(),
            user: {
                ...user.toJSON(),
                myStatus: myStatus?.status || null,
                friendStatus: friendStatus?.status || null,
                followerCount
            }
        };
    }));

    return {
        total: donatorDocs.count,
        donators: donatorsWithFriendStatus,
        page,
        limit
    };
};

module.exports = {
    addDonator,
    getDonators,
    editDonator
}