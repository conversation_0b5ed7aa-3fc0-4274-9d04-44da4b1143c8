'use strict';

module.exports = (sequelize, DataTypes) => {
  const Donator = sequelize.define('Donator', {
    post_id: {
      type: DataTypes.INTEGER
    },
   
    donatedAmount:{
      type: DataTypes.STRING,
    },
    user_id:{
      type: DataTypes.INTEGER
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    created_at:{
      type: DataTypes.DATE
    },
    updated_at:{
      type: DataTypes.DATE
    },
  }, {
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });

    Donator.associate = (models) => {
      Donator.belongsTo(models.Post, { foreignKey: 'post_id', as: 'post' });
      Donator.belongsTo(models.User, { foreignKey: 'user_id', as: 'user' });
    };
  return Donator;
};