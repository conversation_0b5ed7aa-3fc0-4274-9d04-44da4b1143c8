const { catchAsync } = require("../../../utils/catchAsync");
// const { donatorService, tokenService } = require("../services");
// const { donatorService } = require("../../../allservice")
const donatorService = require("../services/donator.service")
const httpStatus = require("http-status");



const {handleRequest} = require("../../../helpers/handleRequest")
  module.exports.addDonator = handleRequest(donatorService.addDonator);
  module.exports.editDonator = handleRequest(donatorService.editDonator);
  module.exports.getDonators = handleRequest(donatorService.getDonators);
 