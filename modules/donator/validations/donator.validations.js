const Joi = require("joi");

module.exports.addDonator = {
    body: Joi.object().keys({
        post_id: Joi.number().required(),
        comment_id: Joi.number().optional().allow(null, ""),
        user_id: Joi.number().optional().allow(null , ""),
        donatedAmount: Joi.string().required()
    }),
};
module.exports.editDonator = {
    body: Joi.object().keys({
        donator_id: Joi.number().optional().allow(null, ""),
        post_id: Joi.number().optional().allow(null, ""),
        user_id: Joi.number().optional().allow(null , ""),
        is_deleted: Joi.boolean().optional().allow(null, "")
    }),
};
module.exports.getDonators = {
    body: Joi.object().keys({
        filters: Joi.object().required(),
        page: Joi.number().required(),
        limit: Joi.number().required()
    }),
};