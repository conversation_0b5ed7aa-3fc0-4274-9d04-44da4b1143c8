'use strict';

const jwt = require("jsonwebtoken");
const config = require("../../../configuration/config");
const moment = require("moment");
const { tokenTypes } = require("../../../configuration/constant");
const otpGenerator = require("otp-generator");
// const { Token, User } = require("../models");
const {Token} = require("../../../models")

const verifyToken = async (token, type) => {
    const payload = jwt.verify(token, config.jwt.secret);
    let tokenDoc = await Token.findOne({ where: { email: payload.sub, type, token } });

    if (!tokenDoc) {
        tokenDoc = await Token.findOne({ where: { mobile_number: payload.sub, type, token } });
        if (!tokenDoc) throw new Error("Token not found.");
    } else {
        tokenDoc = await Token.findOne({ where: { id: payload.sub, type, token } });
        if (!tokenDoc) throw new Error("Token not found.");
    }

    return tokenDoc;
};

const generateToken = (expires, type, email, mobile_number, user_id) => {
    const payload = {
        sub: email ? email : mobile_number ? mobile_number : user_id,
        iat: moment().unix(),
        exp: expires.unix(),
        type
    };
    return jwt.sign(payload, config.jwt.secret);
};

const saveToken = async (expires, type, token, email, otp, password, mobile_number, user_id) => {
    // console.log("email=============>", email)
    const tokenDoc = await Token.create({
        email,
        password,
        expires: expires.toDate(),
        type,
        token,
        mobile_number,
        otp,
        user_id: user_id && user_id !== "" ? user_id : 0
    });

    return tokenDoc;
};

const generateMailVerificationToken = async (email, otp) => {
    const verifyMailTokenExpires = moment().add(config.jwt.mailVerificationExpirationDays, "days");
    const token = generateToken(verifyMailTokenExpires, tokenTypes.MAIL_VERIFY, email);
    await saveToken(verifyMailTokenExpires, tokenTypes.MAIL_VERIFY, token, email, otp, "", "", "");
    return token;
};

const generatemobile_numberVerificationToken = async (mobile_number, otp) => {
    const verifymobile_numberTokenExpires = moment().add(config.jwt.mailVerificationExpirationDays, "days");
    const token = generateToken(verifymobile_numberTokenExpires, tokenTypes.mobile_number_VERIFY, "", mobile_number);
    await saveToken(verifymobile_numberTokenExpires, tokenTypes.mobile_number_VERIFY, token, "", otp, "", mobile_number, "");
    return token;
};

const generateAuthToken = async (user) => {
    const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, "minutes");
    const accessToken = generateToken(accessTokenExpires, tokenTypes.ACCESS, "", "", user?.id);

    const refreshTokenExpires = moment().add(config.jwt.refreshExpirationDays, "days");
    const refreshToken = generateToken(refreshTokenExpires, tokenTypes.REFRESH, "", "", user?.id);

    console.log("refreshToken===",refreshToken)
    // console.log(refreshTokenExpires, tokenTypes.REFRESH, refreshToken, user.email || "", "", "", user.mobile_number || "", user?.id)
    await saveToken(refreshTokenExpires, tokenTypes.REFRESH, refreshToken, user.email || "", "", "", user.mobile_number || "", user?.id);

    return {
        access: {
            token: accessToken,
            expires: accessTokenExpires.toDate()
        },
        refresh: {
            token: refreshToken,
            expires: refreshTokenExpires.toDate()
        }
    };
};

const generateresetPasswordToken = async (user_id, otp) => {
    const verifyResetTokenExpires = moment().add(config.jwt.mailVerificationExpirationDays, "days");
    const token = generateToken(verifyResetTokenExpires, tokenTypes.RESET_PASSWORD_OTP, "", "", user_id);
    await saveToken(verifyResetTokenExpires, tokenTypes.RESET_PASSWORD_OTP, token, "", otp, "", "", user_id);
    return token;
};

const generateOTP = () => {
    return otpGenerator.generate(6, {
        lowerCaseAlphabets: false,
        upperCaseAlphabets: false,
        specialChars: false
    });
};

module.exports = {
    verifyToken,
    generateToken,
    saveToken,
    generateMailVerificationToken,
    generatemobile_numberVerificationToken,
    generateAuthToken,
    generateOTP,
    generateresetPasswordToken
};
