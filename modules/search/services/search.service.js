const {Search, Follower} = require("../../../models")
const httpStatus = require("http-status");
const ApiError = require("../../../utils/ApiError");
const User = require("../../user/models/user.model");
// const activityService = require("../../activity/services/activity.service");
// const { _isObjectId } = require("../../../helpers/global.functions");
// const  mongoose = require("mongoose");
const { Op, Sequelize } = require("sequelize");

const addSearch = async(reqBody) =>{
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }
 const search = await Search.create(reqBody)
 if(reqBody.createdBy){
    // await activityService.addActivity({actionOn: reqBody.createdBy, actionBy: reqBody.createdBy, actionName: "addSearch" , actionDescription: `search name ${reqBody.searchDisplayName}` })
 }
 return search
}
const editSearch = async(reqBody) =>{
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }
    const [updatedCount, updatedSearchs] = await Search.update(reqBody, {
        where: { id: reqBody.search_id },
        returning: true,
    });

    if (updatedCount === 0) {
        throw new ApiError(httpStatus.NOT_FOUND, "Search not found");
    }

    // await activityService.addActivity({
    //     actionOn: user?.id,
    //     actionBy: user?.id,
    //     actionName: "addDetails",
    //     actionDescription: "Adding personal details",
    // });

    return updatedSearchs[0];
}

const getSearchs = async ({ filters, page, limit }, user) => {
    // Clean and transform filters
    Object.entries(filters).forEach(([key, value]) => {
        if (value === "") delete filters[key];
    });

    // Add search functionality
    let searchCondition = {};
    if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        searchCondition = {
            [Op.or]: [
                Sequelize.literal(`LOWER("Search"."search_tag") LIKE '%${searchTerm}%'`)
            ]
        };
    }

    // Add search condition to existing filters
    if (filters.search) {
        filters = {
            [Op.and]: [
                filters,
                searchCondition
            ]
        };
        delete filters.search;
    }else{
        filters = {
            [Op.and]: [
                filters
            ]
        };
    }

    // Define pagination options
    const offset = (page - 1) * limit;

    // Fetch searchs using Sequelize
    const searchDocs = await Search.findAndCountAll({
        where: filters,
        limit,
        offset,
    });

    return {
        total: searchDocs.count,
        searchs: searchDocs.rows,
        page,
        limit
    };
};

module.exports={
    addSearch,
    getSearchs,
    editSearch
}