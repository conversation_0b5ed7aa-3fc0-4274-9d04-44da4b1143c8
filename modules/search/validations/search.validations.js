const Joi = require("joi");

module.exports.addSearch = {
    body: Joi.object().keys({
      search_tag: Joi.string().required()
    }),
};
module.exports.editSearch = {
    body: Joi.object().keys({
        search_id: Joi.number().required(),
        search_tag: Joi.string().optional().allow(null, ""),
        is_deleted: Joi.boolean().optional().allow(null, "")
    }),
};
module.exports.getSearchs = {
    body: Joi.object().keys({
        filters: Joi.object().required(),
        page: Joi.number().required(),
        limit: Joi.number().required()
    }),
};