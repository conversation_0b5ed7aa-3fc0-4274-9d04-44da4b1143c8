const { catchAsync } = require("../../../utils/catchAsync");
// const { searchService, tokenService } = require("../services");
// const { searchService } = require("../../../allservice")
const searchService = require("../services/search.service")
const httpStatus = require("http-status");



const {handleRequest} = require("../../../helpers/handleRequest")
  module.exports.addSearch = handleRequest(searchService.addSearch);
  module.exports.editSearch = handleRequest(searchService.editSearch);
  module.exports.getSearchs = handleRequest(searchService.getSearchs);
 