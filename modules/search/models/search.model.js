'use strict';

module.exports = (sequelize, DataTypes) => {
  const Search = sequelize.define('Search', {
    search_tag:{
      type: DataTypes.STRING,
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    created_at:{
      type: DataTypes.DATE
    },
    updated_at:{
      type: DataTypes.DATE
    },
  }, {
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });

    // Search.associate = (models) => {
    //   Search.belongsTo(models.User, { foreignKey: 'searchged_user_id', as: 'searchgedUser' });
    //   Search.belongsTo(models.Post, { foreignKey: 'post_id', as: 'post' });
    // };
    
  return Search;
};