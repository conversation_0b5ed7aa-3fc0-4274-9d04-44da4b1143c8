const express = require("express");
const router = express.Router();
const validate = require("../../../helpers/validate");
const  searchValidation  = require("../validations/search.validations");
const searchController = require("../controllers/search.controller");
const multer = require('multer');
const { storage } = require("../../../configuration/storage");
const { authorize } = require("../../../authwires/auth");
const upload = multer({ storage: storage });


router.use(authorize)

/**
 * @swagger
 * /search/add-search:
 *   post:
 *     summary: add search
 *     searchs:
 *       - Search
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               search_tag:
 *                 type: string
 *                 description: Display name for the search
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/add-search', validate(searchValidation.addSearch), searchController.addSearch)
/**
 * @swagger
 * /search/edit-search:
 *   post:
 *     summary: edit search
 *     searchs:
 *       - Search
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               search_id:
 *                 type: number
 *               search_tag:
 *                 type: string
 *                 description: Display name for the search
 *               is_deleted:
 *                 type: boolean
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/edit-search', validate(searchValidation.editSearch), searchController.editSearch)



// authorize routes
// router.use(authorize)



/**
 * @swagger
 * /search/get-searchs:
 *   post:
 *     summary: fetch color preferences
 *     searchs:
 *       - Search
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               filters:
 *                 type: object
 *                 description: search name
 *               page:
 *                type: number
 *               limit:
 *                type: number
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/get-searchs', validate(searchValidation.getSearchs), searchController.getSearchs)
// router.post('/get-search', validate(searchValidation.getSearch), searchController.getSearch)
// router.post('/delete-search', validate(searchValidation.deleteSearch), searchController.deleteSearch)


module.exports = router;
