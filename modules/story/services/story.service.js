const { Story, User, StoryLike, StoryComment, Music } = require('../../../models');
const httpStatus = require('http-status');
const ApiError = require('../../../utils/ApiError');
const { Sequelize, Op } = require('sequelize');
const { client: redisClient, set: redisSet, get: redisGet, setHash: redisSetHash, getHash: redisGetHash, getAllHash: redisGetAllHash } = require('../../../services/redis.service');
const logger = require('../../../utils/logger');
const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs').promises;
const axios = require('axios');
const { getSongDetails } = require('../../music/services/music.service');
const Friend = require("../../friend/models/friend.model");
const MediaProcessor = require('../../../helpers/mediaprocess');

// Redis key generators
const getStoryKey = (storyId) => `story:${storyId}`;
const getStoriesListKey = (userId) => `stories:${userId}`;
const getStoriesCountKey = (userId) => `stories:count:${userId}`;
const getMusicKey = (musicId) => `music:${musicId}`;
const getMusicListKey = () => 'music:list';

// FFmpeg conversion function
const convertMedia = async (inputPath, outputPath, options = {}) => {
    return new Promise((resolve, reject) => {
        logger.info(`Starting media conversion: ${inputPath} -> ${outputPath}`);
        
        const command = ffmpeg(inputPath);
        
        // Set output options
        if (options.width) {
            logger.info(`Setting width to ${options.width}`);
            command.size(`${options.width}x?`);
        }
        if (options.quality) {
            logger.info(`Setting quality to ${options.quality}`);
            command.videoBitrate(options.quality);
        }
        if (options.format) {
            logger.info(`Setting format to ${options.format}`);
            command.format(options.format);
        }
        
        command
            .on('start', (commandLine) => {
                logger.info('FFmpeg command:', commandLine);
            })
            .on('progress', (progress) => {
                logger.info('Conversion progress:', progress);
            })
            .on('end', () => {
                logger.info('Media conversion completed successfully');
                resolve(outputPath);
            })
            .on('error', (err) => {
                logger.error('FFmpeg error:', err);
                reject(err);
            })
            .save(outputPath);
    });
};

// Music library functions
const addMusic = async (reqBody) => {
    const { title, artist, duration, file_url, thumbnail_url } = reqBody;
    
    const music = await Music.create({
        title,
        artist,
        duration,
        file_url,
        thumbnail_url
    });

    // Cache the music
    const musicKey = getMusicKey(music.id);
    await redisSet(musicKey, JSON.stringify(music.get({ plain: true })));

    // Update music list
    const musicListKey = getMusicListKey();
    await redisSetHash(musicListKey, music.id.toString(), JSON.stringify(music.get({ plain: true })));

    return music;
};

const getMusicList = async ({ page = 1, limit = 20 }) => {
    const musicListKey = getMusicListKey();
    
    // Try to get from cache first
    const cachedMusic = await redisGetAllHash(musicListKey);
    
    if (cachedMusic) {
        const musicArray = Object.values(cachedMusic).map(m => JSON.parse(m));
        const start = (page - 1) * limit;
        const end = start + limit;
        
        return {
            total: musicArray.length,
            music: musicArray.slice(start, end),
            page,
            limit
        };
    }

    // If not in cache, get from database
    const music = await Music.findAndCountAll({
        limit,
        offset: (page - 1) * limit,
        order: [['created_at', 'DESC']]
    });

    // Cache the results
    const musicHash = {};
    music.rows.forEach(m => {
        const plainMusic = m.get({ plain: true });
        musicHash[m.id] = JSON.stringify(plainMusic);
    });

    await redisSetHash(musicListKey, musicHash);

    return {
        total: music.count,
        music: music.rows,
        page,
        limit
    };
};

const downloadFile = async (url, outputPath) => {
    try {
        logger.info('Downloading file from presigned URL:', url);
        const response = await axios({
            method: 'GET',
            url: url,
            responseType: 'stream',
            timeout: 30000, // 30 second timeout
            maxContentLength: 100 * 1024 * 1024, // 100MB max file size
            validateStatus: function (status) {
                return status >= 200 && status < 300; // Only accept 2xx status codes
            }
        });

        const writer = fs.createWriteStream(outputPath);
        
        return new Promise((resolve, reject) => {
            response.data.pipe(writer);
            writer.on('finish', () => {
                logger.info('File downloaded successfully to:', outputPath);
                resolve();
            });
            writer.on('error', (error) => {
                logger.error('Error writing file:', {
                    error: error.message,
                    code: error.code,
                    path: outputPath
                });
                reject(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error saving downloaded file'));
            });
        });
    } catch (error) {
        logger.error('Error downloading file:', {
            error: error.message,
            code: error.code,
            url: url,
            response: error.response ? {
                status: error.response.status,
                statusText: error.response.statusText,
                headers: error.response.headers
            } : null
        });
        if (error.response) {
            throw new ApiError(error.response.status, 'Error downloading file from URL');
        }
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error downloading media file');
    }
};

// Ensure upload directories exist
const ensureUploadDirectories = async () => {
    const baseDir = path.join(__dirname, '../../../uploads');
    const dirs = [
        baseDir,
        path.join(baseDir, 'media'),
        path.join(baseDir, 'temp'),
        path.join(baseDir, 'converted')
    ];

    for (const dir of dirs) {
        try {
            await fs.mkdir(dir, { recursive: true });
            logger.info(`Directory verified/created: ${dir}`);
        } catch (error) {
            logger.error(`Error creating directory ${dir}:`, error);
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error setting up upload directories');
        }
    }
};

const addStory = async (reqBody, user) => {
    console.log('Adding new story for user:', user.id);
    
    // Clean empty strings
    for (let [key, value] of Object.entries(reqBody)) {
        if (value === '') delete reqBody[key];
    }

    // Handle media conversion if needed
    if (reqBody.media_url) {
        // If the media_url is a direct http(s) URL, skip download/conversion
        if (/^https?:\/\//.test(reqBody.media_url)) {
            logger.info('External media URL detected, skipping download and conversion.');
            // Use the URL as-is
            reqBody.media_url = reqBody.media_url;
        } else {
            // Ensure all required directories exist
            await ensureUploadDirectories();

            const baseDir = path.join(__dirname, '../../../uploads');
            const tempDir = path.join(baseDir, 'temp');
            const convertedDir = path.join(baseDir, 'converted');
            const mediaDir = path.join(baseDir, 'media');
            try {
                // Generate unique filenames
                const timestamp = Date.now();
                const originalFilename = path.basename(reqBody.media_url);
                const tempPath = path.join(tempDir, originalFilename);
                const outputPath = path.join(convertedDir, `converted_${timestamp}_${originalFilename}`);

                // For local files, copy to temp directory
                logger.info('Copying local file to temp directory...');
                const mediaPath = path.join(mediaDir, reqBody.media_url);
                try {
                    // Check if file exists
                    await fs.access(mediaPath);
                    logger.info('Media file found at:', mediaPath);
                    // Get file stats
                    const stats = await fs.stat(mediaPath);
                    logger.info('Media file stats:', {
                        size: stats.size,
                        isFile: stats.isFile(),
                        permissions: stats.mode
                    });
                    // Copy file
                    await fs.copyFile(mediaPath, tempPath);
                    logger.info('Local file copied successfully');
                } catch (error) {
                    logger.error('Error accessing/copying local file:', {
                        error: error.message,
                        code: error.code,
                        path: mediaPath,
                        mediaUrl: reqBody.media_url
                    });
                    throw new ApiError(httpStatus.BAD_REQUEST, `Local media file not found or not accessible at: ${reqBody.media_url}`);
                }
                // Verify the temp file exists and has content
                try {
                    const stats = await fs.stat(tempPath);
                    if (stats.size === 0) {
                        throw new Error('Downloaded file is empty');
                    }
                    logger.info('Temp file verified:', {
                        size: stats.size,
                        path: tempPath
                    });
                } catch (error) {
                    logger.error('Error verifying temp file:', {
                        error: error.message,
                        code: error.code,
                        path: tempPath
                    });
                    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error verifying downloaded file');
                }
                // Convert media to optimized format
                logger.info('Starting media conversion...');
                try {
                    await convertMedia(tempPath, outputPath, {
                        width: 1080,
                        quality: '1000k',
                        format: 'mp4'
                    });
                    logger.info('Media conversion completed successfully');
                } catch (error) {
                    logger.error('Error during media conversion:', {
                        error: error.message,
                        code: error.code,
                        inputPath: tempPath,
                        outputPath: outputPath
                    });
                    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error converting media file');
                }
                // Clean up temp file
                try {
                    await fs.unlink(tempPath);
                    logger.info('Temporary file cleaned up');
                } catch (error) {
                    logger.warn('Error cleaning up temp file:', error);
                }
                // Update media URL to converted file
                reqBody.media_url = path.relative(path.join(__dirname, '../../../uploads'), outputPath);
                logger.info('Updated media URL:', reqBody.media_url);
            } catch (error) {
                logger.error('Error processing media:', {
                    error: error.message,
                    code: error.code,
                    stack: error.stack,
                    mediaUrl: reqBody.media_url
                });
                if (error instanceof ApiError) {
                    throw error;
                }
                throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, `Error processing media file: ${error.message}`);
            }
        }
    }

    // Handle music attachment from JioSaavn
    if (reqBody.music_data) {
        console.log(reqBody.music_data)
        try {

        let musicDetails = reqBody.music_data
        let jsonData = {
            "file_url": musicDetails.file_url,
            "startTime": musicDetails.start_time ? musicDetails.start_time : musicDetails.startTime ? musicDetails.startTime : 0,
            "end_time": musicDetails.end_time || musicDetails.duration,
            "output_format": "mp3"
        }
        const mediaProcessor = new MediaProcessor();
        const result = await mediaProcessor.processMediaFromJson(jsonData);
        console.log("result======================================================", result)
        // console.log(musicDetails)
            reqBody.music_data = {
                title: musicDetails.title,
                artist: musicDetails.artist,
                duration: musicDetails.duration,
                file_url: result.fileUrl || "",
                thumbnail_url: musicDetails.thumbnail_url,
                start_time: musicDetails.start_time || 0,
                end_time: musicDetails.end_time || musicDetails.duration
            };
            delete reqBody.music;
        } catch (error) {
            logger.error('Error attaching JioSaavn music to story:', error);
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error attaching JioSaavn music to story');
        }
    }

    // Add user ID to the story
    reqBody.user_id = user.id;

    const story = await Story.create(reqBody);
    console.log('Created new story with ID:', story.id);

    // Get the complete story with all associations and computed fields
    const completeStory = await Story.findOne({
        replacements: { userId: user.id },
        where: { id: story.id },
        attributes: [
            'id',
            'user_id',
            'content',
            'media_url',
            'location',
            'created_at',
            'music_data',
            'media_type',
            'background',
            'layers',
            'duration',
            [Sequelize.literal('(CASE WHEN "Story"."user_id" = :userId THEN true ELSE false END)'), 'isOwn']
        ],
        include: [
            {
                model: User,
                as: 'user',
                attributes: ['id', 'username', 'email', 'profile_picture_url', 'privacy_status', 'first_name', 'last_name']
            }
        ]
    });

    // Get friend relationships using Friend model (which uses Neo4j internally)
    const myStatus = await Friend.getFriendshipStatus(user.id, completeStory.user_id);
    const friendStatus = await Friend.getFriendshipStatus(completeStory.user_id, user.id);
    const isFriend = myStatus?.status === 'accepted';
    const storyWithFriendStatus = {
        ...completeStory.toJSON(),
        myStatus: myStatus?.status || null,
        friendStatus: friendStatus?.status || null,
        isFriend
    };

    // Cache the new story
    const storyKey = getStoryKey(story.id);
    await redisSet(storyKey, storyWithFriendStatus);
    console.log('Cached individual story with key:', storyKey);

    // Update stories list in Redis
    const storiesListKey = getStoriesListKey(user.id);
    await redisSetHash(storiesListKey, story.id, storyWithFriendStatus);
    console.log('Added story to user\'s stories list with key:', storiesListKey);

    // Update count
    const countKey = getStoriesCountKey(user.id);
    const currentCount = await redisGet(countKey) || 0;
    const newCount = parseInt(currentCount) + 1;
    await redisSet(countKey, newCount);
    console.log('Updated story count to:', newCount);

    return storyWithFriendStatus;
};

const editStory = async (reqBody, user) => {
    for (let [key, value] of Object.entries(reqBody)) {
        if (value === '') delete reqBody[key];
    }

    let updatedStory = await Story.findOne({ where: { id: reqBody.story_id } });
    let updatedCount = updatedStory ? 1 : 0;

    if (updatedCount === 0) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Story not found');
    }

    await updatedStory.update(reqBody);

    // Get the complete updated story with all associations and computed fields
    const completeStory = await Story.findOne({
        replacements: { userId: user.id },
        where: { id: reqBody.story_id },
        attributes: [
            'id',
            'user_id',
            'content',
            'media_url',
            'location',
            'created_at',
            'music_data',
            'media_type',
            'background',
            'layers',
            'duration',
            [Sequelize.literal('(CASE WHEN "Story"."user_id" = :userId THEN true ELSE false END)'), 'isOwn']
        ],
        include: [
            {
                model: User,
                as: 'user',
                attributes: ['id', 'username', 'email', 'profile_picture_url', 'privacy_status', 'first_name', 'last_name']
            }
        ]
    });

    // Get friend relationships using Friend model (which uses Neo4j internally)
    const myStatus = await Friend.getFriendshipStatus(user.id, completeStory.user_id);
    const friendStatus = await Friend.getFriendshipStatus(completeStory.user_id, user.id);
    const isFriend = myStatus?.status === 'accepted';
    const storyWithFriendStatus = {
        ...completeStory.toJSON(),
        myStatus: myStatus?.status || null,
        friendStatus: friendStatus?.status || null,
        isFriend
    };

    // Update story in cache
    await redisSet(getStoryKey(reqBody.story_id), storyWithFriendStatus);

    // Update story in all users' lists
    const allKeys = await redisClient.keys('stories:*');
    for (const key of allKeys) {
        if (key.includes(':count:')) continue; // Skip count keys
        
        const existingStories = await redisGetAllHash(key) || {};
        if (existingStories[reqBody.story_id]) {
            await redisSetHash(key, reqBody.story_id, storyWithFriendStatus);
        }
    }

    return storyWithFriendStatus;
};

const getStories = async ({ filters, page, limit }, user) => {
    // Try to get from cache first
    let stories = [];

    // Get user's own stories only if friend_only is not true
    if (!filters?.friend_only) {
        const userStoriesKey = getStoriesListKey(user.id);
        const userStories = await redisGetAllHash(userStoriesKey);
        
        if (userStories && Object.keys(userStories).length > 0) {
            const parsedUserStories = Object.values(userStories).map(story => {
                try {
                    if (typeof story === 'object' && story !== null) {
                        return story;
                    }
                    return JSON.parse(story);
                } catch (error) {
                    console.error('Error parsing user story:', error);
                    return null;
                }
            }).filter(Boolean);
            
            stories = stories.concat(parsedUserStories);
        }
    }

    // Get friends' stories if friend_only is true or no filters are provided
    if (filters?.friend_only || !filters) {
        // Get all friends' stories
        const friends = await Friend.findAll({
            where: {
                [Op.or]: [
                    { user_id_1: user.id },
                    { user_id_2: user.id }
                ],
                status: 'accepted',
                is_deleted: false
            }
        });

        // Get stories from each friend's cache
        for (const friend of friends) {
            const friendId = friend.user_id_1 === user.id ? friend.user_id_2 : friend.user_id_1;
            const friendStoriesKey = getStoriesListKey(friendId);
            const friendStories = await redisGetAllHash(friendStoriesKey);
            
            if (friendStories && Object.keys(friendStories).length > 0) {
                const parsedStories = Object.values(friendStories).map(story => {
                    try {
                        if (typeof story === 'object' && story !== null) {
                            return story;
                        }
                        return JSON.parse(story);
                    } catch (error) {
                        console.error('Error parsing friend story:', error);
                        return null;
                    }
                }).filter(Boolean);
                
                stories = stories.concat(parsedStories);
            }
        }
    }

    if (stories.length > 0) {
        console.log('Using cached stories, total before filtering:', stories.length);

        // Apply filters
        if (filters?.user_id) {
            stories = stories.filter(story => story.user_id === filters.user_id);
        }

        if (filters?.media_type) {
            stories = stories.filter(story => 
                story.media_url && story.media_url.includes(filters.media_type)
            );
        }

        // Sort by created_at in descending order
        stories.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

        // Apply pagination
        const start = (page - 1) * limit;
        const end = start + limit;
        const paginatedStories = stories.slice(start, end);

        // After fetching storyDocs.rows, attach friend status to each story
        const storiesWithFriendStatus = await Promise.all(paginatedStories.map(async (story) => {
            const myStatus = await Friend.getFriendshipStatus(user.id, story.user_id);
            const friendStatus = await Friend.getFriendshipStatus(story.user_id, user.id);
            const isFriend = myStatus?.status === 'accepted';
            return {
                ...story,
                myStatus: myStatus?.status || null,
                friendStatus: friendStatus?.status || null,
                isFriend
            };
        }));

        return {
            total: stories.length,
            stories: storiesWithFriendStatus,
            page,
            limit
        };
    }

    // If not in cache, query from database
    const offset = (page - 1) * limit;
    let whereClause = { is_deleted: false };

    // Apply filters
    if (filters?.user_id) {
        whereClause.user_id = filters.user_id;
    }

    if (filters?.media_type) {
        whereClause.media_url = {
            [Op.contains]: [{ media_type: filters.media_type }]
        };
    }

    // Add friend filter if needed
    let includeClause = [
        {
            model: User,
            as: 'user',
            attributes: ['id', 'username', 'email', 'profile_picture_url', 'privacy_status', 'first_name', 'last_name']
        }
    ];

    if (filters?.friend_only) {
        // When friend_only is true, only get friends' stories
        includeClause.push({
            model: Friend,
            as: 'friends',
            where: {
                [Op.or]: [
                    { user_id_1: user.id },
                    { user_id_2: user.id }
                ],
                status: 'accepted',
                is_deleted: false
            },
            required: true
        });
        // Exclude user's own stories
        whereClause.user_id = { [Op.ne]: user.id };
    } else if (!filters) {
        // When no filters, include both own and friends' stories
        includeClause.push({
            model: Friend,
            as: 'friends',
            where: {
                [Op.or]: [
                    { user_id_1: user.id },
                    { user_id_2: user.id }
                ],
                status: 'accepted',
                is_deleted: false
            },
            required: false
        });
    }

    const storyDocs = await Story.findAndCountAll({
        replacements: { userId: user.id },
        where: whereClause,
        attributes: [
            'id',
            'user_id',
            'content',
            'media_url',
            'location',
            'created_at',
            'music_data',
            'media_type',
            'background',
            'layers',
            'duration',
            [Sequelize.literal('(CASE WHEN "Story"."user_id" = :userId THEN true ELSE false END)'), 'isOwn']
        ],
        include: includeClause,
        order: [['created_at', 'DESC']],
        limit,
        offset
    });

    // After fetching storyDocs.rows, attach friend status to each story
    const storiesWithFriendStatus = await Promise.all(storyDocs.rows.map(async (story) => {
        const myStatus = await Friend.getFriendshipStatus(user.id, story.user_id);
        const friendStatus = await Friend.getFriendshipStatus(story.user_id, user.id);
        const isFriend = myStatus?.status === 'accepted';
        return {
            ...story.toJSON(),
            myStatus: myStatus?.status || null,
            friendStatus: friendStatus?.status || null,
            isFriend
        };
    }));

    // Cache individual stories
    for (const story of storiesWithFriendStatus) {
        const storyKey = getStoryKey(story.id);
        const plainStory = story.get({ plain: true });
        await redisSet(storyKey, JSON.stringify(plainStory));

        // Add to user's stories list
        const storiesListKey = getStoriesListKey(story.user_id);
        await redisSetHash(storiesListKey, story.id, JSON.stringify(plainStory));
    }

    // Update count for each user
    const userCounts = {};
    for (const story of storiesWithFriendStatus) {
        if (!userCounts[story.user_id]) {
            userCounts[story.user_id] = 0;
        }
        userCounts[story.user_id]++;
    }

    // Update Redis counts
    for (const [userId, count] of Object.entries(userCounts)) {
        const countKey = getStoriesCountKey(userId);
        await redisSet(countKey, count);
    }

    return {
        total: storyDocs.count,
        stories: storiesWithFriendStatus,
        page,
        limit
    };
};

const getStory = async ({ id }, user) => {
    const story = await Story.findOne({
        replacements: { userId: user.id },
        where: { id, is_deleted: false },
        attributes: [
            'id',
            'user_id',
            'content',
            'media_url',
            'location',
            'created_at',
            'music_data',
            'media_type',
            'background',
            'layers',
            'duration',
            [Sequelize.literal('(CASE WHEN "Story"."user_id" = :userId THEN true ELSE false END)'), 'isOwn']
        ],
        include: [
            {
                model: User,
                as: 'user',
                attributes: ['id', 'username', 'email', 'profile_picture_url', 'privacy_status', 'first_name', 'last_name']
            }
        ]
    });

    if (!story) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Story not found');
    }

    // Get friend relationships using Friend model (which uses Neo4j internally)
    const myStatus = await Friend.getFriendshipStatus(user.id, story.user_id);
    const friendStatus = await Friend.getFriendshipStatus(story.user_id, user.id);
    const isFriend = myStatus?.status === 'accepted';

    // Attach to returned object
    const storyWithFriendStatus = {
        ...story.toJSON(),
        myStatus: myStatus?.status || null,
        friendStatus: friendStatus?.status || null,
        isFriend
    };

    return storyWithFriendStatus;
};

const getStoryById = async (data, user) => {
    const { id } = data;
    
    const story = await Story.findOne({
        where: { id, is_deleted: false },
        include: [
            {
                model: User,
                as: 'user',
                attributes: ['id', 'username', 'profile_picture_url']
            },
            {
                model: StoryLike,
                as: 'likes',
                attributes: ['id', 'user_id']
            },
            {
                model: StoryComment,
                as: 'comments',
                attributes: ['id', 'content', 'user_id', 'created_at'],
                include: [{
                    model: User,
                    as: 'user',
                    attributes: ['id', 'username', 'profile_picture_url']
                }]
            }
        ]
    });

    if (!story) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Story not found');
    }

    return story;
};

const getUserStories = async (data, user) => {
    const { user_id } = data;
    const stories = await Story.findAll({
        where: { 
            user_id,
            is_deleted: false,
            created_at: {
                [Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
            }
        },
        include: [
            {
                model: User,
                as: 'user',
                attributes: ['id', 'username', 'profile_picture']
            },
            {
                model: StoryLike,
                as: 'likes',
                attributes: ['id', 'user_id']
            }
        ],
        order: [['created_at', 'DESC']]
    });

    return stories;
};

const deleteStory = async (data, user) => {
    const { story_id } = data;

    const story = await Story.findOne({
        where: { id: story_id, user_id: user.id }
    });

    if (!story) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Story not found');
    }

    // Soft delete the story
    await story.update({ is_deleted: true });

    // Delete associated likes and comments
    await StoryLike.destroy({ where: { story_id } });
    await StoryComment.destroy({ where: { story_id } });

    // Update Redis cache
    try {
        // Remove story from individual cache
        const storyKey = getStoryKey(story_id);
        await redisClient.del(storyKey);

        // Remove story from user's stories list
        const storiesListKey = getStoriesListKey(user.id);
        await redisClient.hdel(storiesListKey, story_id);

        // Update story count
        const countKey = getStoriesCountKey(user.id);
        const currentCount = await redisGet(countKey);
        if (currentCount) {
            const newCount = Math.max(0, parseInt(currentCount) - 1);
            await redisSet(countKey, newCount);
            logger.info(`Updated story count for user ${user.id} to ${newCount}`);
        }

        logger.info(`Successfully removed story ${story_id} from Redis cache`);
    } catch (error) {
        logger.error('Error updating Redis cache after story deletion:', error);
        // Don't throw error as the story is already deleted in the database
    }

    return { message: 'Story and associated data deleted successfully' };
};

const likeStory = async (data, user) => {
    const { story_id } = data;

    const story = await Story.findOne({
        where: { id: story_id, is_deleted: false }
    });

    if (!story) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Story not found');
    }

    const [like, created] = await StoryLike.findOrCreate({
        where: { story_id, user_id: user.id },
        defaults: { story_id, user_id: user.id }
    });

    if (!created) {
        await like.destroy();
        await story.decrement('likes_count');
        return { 
            message: 'Story unliked successfully',
            liked: false,
            likes_count: story.likes_count - 1
        };
    }

    await story.increment('likes_count');
    return { 
        message: 'Story liked successfully',
        liked: true,
        likes_count: story.likes_count + 1
    };
};

const commentStory = async (data, user) => {
    const { story_id, content, parent_id } = data;

    const story = await Story.findOne({
        where: { id: story_id, is_deleted: false }
    });

    if (!story) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Story not found');
    }

    // If this is a reply, verify parent comment exists
    if (parent_id) {
        const parentComment = await StoryComment.findOne({
            where: { id: parent_id, story_id, is_deleted: false }
        });
        if (!parentComment) {
            throw new ApiError(httpStatus.NOT_FOUND, 'Parent comment not found');
        }
    }

    const comment = await StoryComment.create({
        story_id,
        user_id: user.id,
        content,
        parent_id
    });

    await story.increment('comments_count');

    const commentWithUser = await StoryComment.findOne({
        where: { id: comment.id },
        include: [{
            model: User,
            as: 'user',
            attributes: ['id', 'username', 'profile_picture']
        }]
    });

    return {
        comment: commentWithUser,
        comments_count: story.comments_count + 1
    };
};

const getStoryComments = async (data, user) => {
    const { story_id, page = 1, limit = 20 } = data;
    const offset = (page - 1) * limit;

    // Verify story exists
    const story = await Story.findOne({
        where: { id: story_id, is_deleted: false }
    });

    if (!story) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Story not found');
    }

    const comments = await StoryComment.findAndCountAll({
        where: { 
            story_id,
            parent_id: null, // Get only top-level comments
            is_deleted: false
        },
        include: [
            {
                model: User,
                as: 'user',
                attributes: ['id', 'username', 'profile_picture']
            },
            {
                model: StoryComment,
                as: 'replies',
                where: { is_deleted: false },
                required: false,
                include: [{
                    model: User,
                    as: 'user',
                    attributes: ['id', 'username', 'profile_picture']
                }]
            }
        ],
        order: [
            ['created_at', 'DESC'],
            [{ model: StoryComment, as: 'replies' }, 'created_at', 'ASC']
        ],
        limit,
        offset
    });

    return {
        comments: comments.rows,
        total: comments.count,
        page,
        totalPages: Math.ceil(comments.count / limit)
    };
};

const deleteComment = async (data, user) => {
    const { comment_id } = data;

    const comment = await StoryComment.findOne({
        where: { id: comment_id, user_id: user.id },
        include: [{
            model: Story,
            as: 'story'
        }]
    });

    if (!comment) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Comment not found');
    }

    // If it's a parent comment, also delete all replies
    if (!comment.parent_id) {
        await StoryComment.update(
            { is_deleted: true },
            { where: { parent_id: comment_id } }
        );
    }

    // Soft delete the comment
    await comment.update({ is_deleted: true });

    // Update story comments count
    const deletedCount = await StoryComment.count({
        where: { 
            story_id: comment.story_id,
            is_deleted: true
        }
    });

    await comment.story.update({
        comments_count: comment.story.comments_count - deletedCount
    });

    return { 
        message: 'Comment deleted successfully',
        comments_count: comment.story.comments_count - deletedCount
    };
};

const getFollowersStories = async (data, user) => {
    if (!user || !user.id) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'User information is required');
    }

    // Extract pagination parameters with defaults
    const page = parseInt(data?.page) || 1;
    const limit = parseInt(data?.limit) || 10;
    const offset = (page - 1) * limit;

    // Try to get from Redis cache first
    const cacheKey = `followers_stories:${user.id}:${page}:${limit}`;
    const cachedStories = await redisGet(cacheKey);
    if (cachedStories) {
        return JSON.parse(cachedStories);
    }

    try {
        // Get all followers who accepted friend requests
        const followers = await Friend.findAll({
            where: {
                [Op.or]: [
                    { user_id_1: user.id },
                    { user_id_2: user.id }
                ],
                status: 'accepted',
                is_deleted: false
            },
            include: [
                {
                    model: User,
                    as: 'User1',
                    attributes: ['id', 'username', 'profile_picture_url', 'first_name', 'last_name']
                },
                {
                    model: User,
                    as: 'User2',
                    attributes: ['id', 'username', 'profile_picture_url', 'first_name', 'last_name']
                }
            ]
        });

        // Get follower IDs (excluding the current user)
        const followerIds = followers.map(follower => 
            follower.user_id_1 === user.id ? follower.user_id_2 : follower.user_id_1
        );

        if (followerIds.length === 0) {
            return {
                total: 0,
                stories: [],
                page,
                limit,
                totalPages: 0
            };
        }

        // Get stories from Redis cache for each follower
        const storiesByUser = {};
        const now = Date.now();
        const twentyFourHoursAgo = now - (24 * 60 * 60 * 1000);

        // Process each follower
        for (const followerId of followerIds) {
            // Get stories list from cache using the same key structure as addStory
            const storiesListKey = getStoriesListKey(followerId);
            const followerStories = await redisGetAllHash(storiesListKey);

            if (followerStories && Object.keys(followerStories).length > 0) {
                // Find the follower's user data
                const follower = followers.find(f => 
                    (f.user_id_1 === followerId && f.user_id_2 === user.id) || 
                    (f.user_id_2 === followerId && f.user_id_1 === user.id)
                );

                if (!follower) {
                    logger.warn(`Follower data not found for ID: ${followerId}`);
                    continue;
                }

                const userData = follower.user_id_1 === followerId ? follower.User1 : follower.User2;
                
                if (!userData) {
                    logger.warn(`User data not found for follower ID: ${followerId}`);
                    continue;
                }

                // Initialize user entry if not exists
                if (!storiesByUser[followerId]) {
                    storiesByUser[followerId] = {
                        id: followerId,
                        name: userData.username,
                        profile_pic_url: userData.profile_picture_url,
                        stories: []
                    };
                }

                // Process each story from cache
                for (const [storyId, storyData] of Object.entries(followerStories)) {
                    try {
                        // Get the complete story from cache using the same key structure as addStory
                        const storyKey = getStoryKey(storyId);
                        const cachedStory = await redisGet(storyKey);
                        
                        if (!cachedStory) {
                            continue;
                        }

                        const story = typeof cachedStory === 'string' ? JSON.parse(cachedStory) : cachedStory;
                        
                        // Only include stories from last 24 hours
                        const storyDate = new Date(story.created_at).getTime();
                        if (storyDate >= twentyFourHoursAgo) {
                            storiesByUser[followerId].stories.push({
                                id: story.id,
                                content: story.content,
                                media_url: story.media_url,
                                media_type: story.media_type,
                                created_at: story.created_at,
                                likes_count: story.likes_count || 0,
                                comments_count: story.comments_count || 0,
                                background: story.background,
                                music_data: story.music_data,
                                layers: story.layers,
                                duration: story.duration,
                                location: story.location
                            });
                        }
                    } catch (error) {
                        logger.error('Error processing cached story:', error);
                        continue;
                    }
                }

                // Sort stories by created_at in descending order
                storiesByUser[followerId].stories.sort((a, b) => 
                    new Date(b.created_at) - new Date(a.created_at)
                );
            }
        }

        // Convert to array and sort by most recent story
        const allStories = Object.values(storiesByUser)
            .filter(user => user.stories.length > 0) // Only include users with stories
            .sort((a, b) => {
                const aLatestStory = a.stories[0]?.created_at || 0;
                const bLatestStory = b.stories[0]?.created_at || 0;
                return new Date(bLatestStory) - new Date(aLatestStory);
            });

        // Apply pagination
        const total = allStories.length;
        const totalPages = Math.ceil(total / limit);
        const paginatedStories = allStories.slice(offset, offset + limit);

        const result = {
            total,
            stories: paginatedStories,
            page,
            limit,
            totalPages
        };

        // Cache the result in Redis for 5 minutes
        await redisSet(cacheKey, JSON.stringify(result), 300);

        return result;
    } catch (error) {
        logger.error('Error in getFollowersStories:', error);
        throw new ApiError(
            httpStatus.INTERNAL_SERVER_ERROR,
            'Error fetching followers stories'
        );
    }
};

module.exports = {
    addStory,
    editStory,
    getStories,
    getStory,
    getStoryById,
    getUserStories,
    deleteStory,
    likeStory,
    commentStory,
    getStoryComments,
    deleteComment,
    addMusic,
    getMusicList,
    getFollowersStories
}; 