'use strict';

module.exports = (sequelize, DataTypes) => {
    const Story = sequelize.define('Story', {
        user_id: {
            type: DataTypes.INTEGER,
           
        },
        content: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        background: {
            type: DataTypes.JSON,
            defaultValue: null,
            allowNull: true
        },
        music_data: {
            type: DataTypes.JSONB,
            allowNull: true,
            defaultValue: null
        },
        layers: {
            type: DataTypes.JSON,
            defaultValue: []
        },
        duration: {
            type: DataTypes.INTEGER,
            defaultValue: 15
        },
        media_url: {
            type: DataTypes.STRING,
            allowNull: true
        },
        media_type:{
            type: DataTypes.STRING,
            allowNull: true
        },
        location: {
            type: DataTypes.STRING,
            allowNull: true
        },
        latitude: {
            type: DataTypes.STRING,
            allowNull: true
        },
        longitude: {
            type: DataTypes.STRING,
            allowNull: true
        },
        music_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        likes_count: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        comments_count: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        is_deleted: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        },
        created_at: {
            type: DataTypes.DATE
        },
        updated_at: {
            type: DataTypes.DATE
        }
    }, {
        timestamps: true,
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      });

    Story.associate = (models) => {
        Story.belongsTo(models.User, {
            foreignKey: 'user_id',
            as: 'user'
        });
        
        Story.belongsTo(models.Music, {
            foreignKey: 'music_id',
            as: 'music_info'
        });
        
        Story.hasMany(models.StoryLike, {
            foreignKey: 'story_id',
            as: 'likes'
        });
        
        Story.hasMany(models.StoryComment, {
            foreignKey: 'story_id',
            as: 'comments'
        });
    };

    return Story;
}; 