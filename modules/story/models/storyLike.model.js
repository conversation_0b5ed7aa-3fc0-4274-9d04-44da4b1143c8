'use strict';

module.exports = (sequelize, DataTypes) => {
  const StoryLike = sequelize.define('StoryLike', {
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    story_id: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    created_at: {
      type: DataTypes.DATE
    },
    updated_at: {
      type: DataTypes.DATE
    }
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  StoryLike.associate = (models) => {
    StoryLike.belongsTo(models.User, { foreignKey: 'user_id', as: 'user' });
    StoryLike.belongsTo(models.Story, { foreignKey: 'story_id', as: 'story' });
  };

  return StoryLike;
}; 