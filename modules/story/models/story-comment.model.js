'use strict';

module.exports = (sequelize, DataTypes) => {
  const StoryComment = sequelize.define('StoryComment', {
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    story_id: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    parent_id: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    created_at: {
      type: DataTypes.DATE
    },
    updated_at: {
      type: DataTypes.DATE
    }
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  StoryComment.associate = (models) => {
    StoryComment.belongsTo(models.User, { foreignKey: 'user_id', as: 'user' });
    StoryComment.belongsTo(models.Story, { foreignKey: 'story_id', as: 'story' });
    StoryComment.belongsTo(models.StoryComment, { foreignKey: 'parent_id', as: 'parent' });
    StoryComment.hasMany(models.StoryComment, { foreignKey: 'parent_id', as: 'replies' });
  };

  return StoryComment;
}; 