const Joi = require('joi');

const addStory = {
  body: Joi.object({
    content: Joi.string().required(),
    background: Joi.object({
      type: Joi.string().valid('image', 'video').required(),
      url: Joi.string().uri().required()
    }).allow(null),
    music: Joi.object({
      title: Joi.string().required(),
      url: Joi.string().uri().required(),
      startTime: Joi.number().min(0)
    }).allow(null),
    layers: Joi.array().items(
      Joi.object({
        type: Joi.string().valid('text', 'emoji', 'image', 'video').required(),
        text: Joi.string().when('type', { is: 'text', then: Joi.required() }),
        emoji: Joi.string().when('type', { is: 'emoji', then: Joi.required() }),
        position: Joi.object({
          x: Joi.number().required(),
          y: Joi.number().required()
        }).required(),
        scale: Joi.number().min(0).default(1),
        rotation: Joi.number().default(0),
        style: Joi.object({
          fontSize: Joi.number(),
          color: Joi.string(),
          fontWeight: Joi.string()
        }).when('type', { is: 'text', then: Joi.required() })
      })
    ).default([]),
    duration: Joi.number().min(1).max(60).default(15),
    location: Joi.string().allow(null, ''),
    latitude: Joi.string().allow(null, ''),
    longitude: Joi.string().allow(null, '')
  })
};

const getStories = {
  body: Joi.object({
    filters: Joi.object({
      user_id: Joi.number().integer(),
      friends_only: Joi.boolean()
    }),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(50).default(20)
  })
};

const getStoryById = {
  query: Joi.object({
    id: Joi.number().integer().required()
  })
};

const getUserStories = {
  query: Joi.object({
    user_id: Joi.number().integer().required()
  })
};

const editStory = {
  body: Joi.object({
    story_id: Joi.number().integer().required(),
    content: Joi.string(),
    background: Joi.object({
      type: Joi.string().valid('image', 'video'),
      url: Joi.string().uri()
    }).allow(null),
    music: Joi.object({
      title: Joi.string(),
      url: Joi.string().uri(),
      startTime: Joi.number().min(0)
    }).allow(null),
    layers: Joi.array().items(
      Joi.object({
        type: Joi.string().valid('text', 'emoji', 'image', 'video'),
        text: Joi.string(),
        emoji: Joi.string(),
        position: Joi.object({
          x: Joi.number(),
          y: Joi.number()
        }),
        scale: Joi.number().min(0),
        rotation: Joi.number(),
        style: Joi.object({
          fontSize: Joi.number(),
          color: Joi.string(),
          fontWeight: Joi.string()
        })
      })
    ),
    duration: Joi.number().min(1).max(60),
    location: Joi.string().allow(null, ''),
    latitude: Joi.string().allow(null, ''),
    longitude: Joi.string().allow(null, '')
  })
};

const deleteStory = {
  body: Joi.object({
    story_id: Joi.number().integer().required()
  })
};

const likeStory = {
  body: Joi.object({
    story_id: Joi.number().integer().required()
  })
};

const commentStory = {
  body: Joi.object({
    story_id: Joi.number().integer().required(),
    content: Joi.string().required(),
    parent_id: Joi.number().integer().allow(null)
  })
};

const getStoryComments = {
  body: Joi.object({
    story_id: Joi.number().integer().required(),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(50).default(20)
  })
};

const deleteComment = {
  body: Joi.object({
    comment_id: Joi.number().integer().required()
  })
};

// module.exports = {
//   addStory,
//   getStories,
//   getStoryById,
//   getUserStories,
//   editStory,
//   deleteStory,
//   likeStory,
//   commentStory,
//   getStoryComments,
//   deleteComment
// }; 