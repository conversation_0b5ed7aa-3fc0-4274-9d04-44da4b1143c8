const express = require('express');
const router = express.Router();
const validate = require('../../../helpers/validate');
const storyValidation = require('../validations/story.validations');
const storyController = require('../controllers/story.controller');
const { authorize } = require('../../../authwires/auth');

router.use(authorize);

/**
 * @swagger
 * /story/add-story:
 *   post:
 *     summary: Add a new story
 *     tags:
 *       - Story
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: body
 *         in: body
 *         required: true
 *         schema:
 *           type: object
 *           required:
 *             - content
 *           properties:
 *             content:
 *               type: string
 *             media_type:
 *               type: string
 *             background:
 *               type: object
 *               properties:
 *                 type:
 *                   type: string
 *                   enum: [image, video]
 *                 url:
 *                   type: string
 *                 color1:
 *                  type: string
 *                 color2:
 *                  type: string
 *             music_data:
 *               type: object
 *               properties:
 *                 title:
 *                   type: string
 *                 artist:
 *                   type: string
 *                 file_url:
 *                   type: string
 *                 startTime:
 *                   type: string
 *                 end_time:
 *                   type: string
 *                 thumbnail_url:
 *                   type: string
 *                 duration:
 *                   type: number
 *             layers:
 *               type: array
 *               items:
 *                 type: object
 *             duration:
 *               type: number
 *             location:
 *               type: string
 *             latitude:
 *               type: string
 *             longitude:
 *               type: string
 *     responses:
 *       200:
 *         description: Story created successfully
 *       400:
 *         description: Bad Request
 *       500:
 *         description: Server Error
 */
router.post('/add-story', validate(storyValidation.addStory), storyController.addStory);

/**
 * @swagger
 * /story/get-stories:
 *   post:
 *     summary: Get stories with filters
 *     tags:
 *       - Story
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: body
 *         in: body
 *         required: true
 *         schema:
 *           type: object
 *           properties:
 *             filters:
 *               type: object
 *               properties:
 *                 user_id:
 *                   type: number
 *                 friends_only:
 *                   type: boolean
 *             page:
 *               type: number
 *             limit:
 *               type: number
 *     responses:
 *       200:
 *         description: Stories retrieved successfully
 */
router.post('/get-stories', validate(storyValidation.getStories), storyController.getStories);

/**
 * @swagger
 * /story/get-story-by-id:
 *   get:
 *     summary: Get story by ID
 *     tags:
 *       - Story
 *     security:
 *       - Token: []
 *     parameters:
 *       - name: id
 *         in: query
 *         required: true
 *         type: number
 *     responses:
 *       200:
 *         description: Story retrieved successfully
 */
router.get('/get-story-by-id', validate(storyValidation.getStoryById), storyController.getStoryById);

/**
 * @swagger
 * /story/get-user-stories:
 *   get:
 *     summary: Get stories by user ID
 *     tags:
 *       - Story
 *     security:
 *       - Token: []
 *     parameters:
 *       - name: user_id
 *         in: query
 *         required: true
 *         type: number
 *     responses:
 *       200:
 *         description: User stories retrieved successfully
 */
router.get('/get-user-stories', validate(storyValidation.getUserStories), storyController.getUserStories);

/**
 * @swagger
 * /story/edit-story:
 *   post:
 *     summary: Edit story
 *     tags:
 *       - Story
 *     security:
 *       - Token: []
 *     parameters:
 *       - name: body
 *         in: body
 *         required: true
 *         schema:
 *           $ref: '#/definitions/Story'
 *     responses:
 *       200:
 *         description: Story updated successfully
 */
router.post('/edit-story', validate(storyValidation.editStory), storyController.editStory);

/**
 * @swagger
 * /story/delete-story:
 *   post:
 *     summary: Delete story
 *     tags:
 *       - Story
 *     security:
 *       - Token: []
 *     parameters:
 *       - name: body
 *         in: body
 *         required: true
 *         schema:
 *           type: object
 *           required:
 *             - story_id
 *           properties:
 *             story_id:
 *               type: number
 *     responses:
 *       200:
 *         description: Story deleted successfully
 */
router.post('/delete-story', validate(storyValidation.deleteStory), storyController.deleteStory);

/**
 * @swagger
 * /story/like-story:
 *   post:
 *     summary: Like/Unlike story
 *     tags:
 *       - Story
 *     security:
 *       - Token: []
 *     parameters:
 *       - name: body
 *         in: body
 *         required: true
 *         schema:
 *           type: object
 *           required:
 *             - story_id
 *           properties:
 *             story_id:
 *               type: number
 *     responses:
 *       200:
 *         description: Story like status updated successfully
 */
router.post('/like-story', validate(storyValidation.likeStory), storyController.likeStory);

/**
 * @swagger
 * /story/comment-story:
 *   post:
 *     summary: Add comment to story
 *     tags:
 *       - Story
 *     security:
 *       - Token: []
 *     parameters:
 *       - name: body
 *         in: body
 *         required: true
 *         schema:
 *           type: object
 *           required:
 *             - story_id
 *             - content
 *           properties:
 *             story_id:
 *               type: number
 *             content:
 *               type: string
 *             parent_id:
 *               type: number
 *     responses:
 *       200:
 *         description: Comment added successfully
 */
router.post('/comment-story', validate(storyValidation.commentStory), storyController.commentStory);

/**
 * @swagger
 * /story/get-story-comments:
 *   post:
 *     summary: Get comments for a story
 *     tags:
 *       - Story
 *     security:
 *       - Token: []
 *     parameters:
 *       - name: body
 *         in: body
 *         required: true
 *         schema:
 *           type: object
 *           required:
 *             - story_id
 *           properties:
 *             story_id:
 *               type: number
 *             page:
 *               type: number
 *               description: Page number for pagination
 *             limit:
 *               type: number
 *               description: Number of comments per page
 *     responses:
 *       200:
 *         description: Comments retrieved successfully
 */
router.post('/get-story-comments', validate(storyValidation.getStoryComments), storyController.getStoryComments);

/**
 * @swagger
 * /story/delete-comment:
 *   post:
 *     summary: Delete a story comment
 *     tags:
 *       - Story
 *     security:
 *       - Token: []
 *     parameters:
 *       - name: body
 *         in: body
 *         required: true
 *         schema:
 *           type: object
 *           required:
 *             - comment_id
 *           properties:
 *             comment_id:
 *               type: number
 *     responses:
 *       200:
 *         description: Comment deleted successfully
 */
router.post('/delete-comment', validate(storyValidation.deleteComment), storyController.deleteComment);

/**
 * @swagger
 * /story/get-followers-stories:
 *   get:
 *     summary: Get stories from followers who accepted friend requests
 *     tags:
 *       - Story
 *     security:
 *       - Token: []
 *     parameters:
 *       - name: page
 *         in: query
 *         required: true
 *         type: number
 *       - name: limit
 *         in: query
 *         required: true
 *         type: number
 *     responses:
 *       200:
 *         description: Stories retrieved successfully
 *         schema:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               id:
 *                 type: number
 *                 description: User ID
 *               name:
 *                 type: string
 *                 description: Username
 *               profile_pic_url:
 *                 type: string
 *                 description: Profile picture URL
 *               stories:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: number
 *                     content:
 *                       type: string
 *                     media_url:
 *                       type: string
 *                     media_type:
 *                       type: string
 *                     created_at:
 *                       type: string
 *                       format: date-time
 *                     likes_count:
 *                       type: number
 *                     comments_count:
 *                       type: number
 */
router.get('/get-followers-stories', storyController.getFollowersStories);

module.exports = router; 