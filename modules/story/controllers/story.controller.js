const { Story, User, StoryLike, StoryComment } = require("../../../models")
const { Op } = require('sequelize');
const httpStatus = require('http-status');
const ApiError = require('../../../utils/ApiError');
const { catchAsync } = require('../../../utils/catchAsync');
const storyService = require('../services/story.service');

const {handleRequest} = require("../../../helpers/handleRequest")






module.exports = {
  addStory: handleRequest(storyService.addStory),
  getStories: handleRequest(storyService.getStories),
  getStoryById: handleRequest(storyService.getStoryById, true),
  getUserStories: handleRequest(storyService.getUserStories, true),
  editStory: handleRequest(storyService.editStory),
  deleteStory: handleRequest(storyService.deleteStory),
  likeStory: handleRequest(storyService.likeStory),
  commentStory: handleRequest(storyService.commentStory),
  getStoryComments: handleRequest(storyService.getStoryComments),
  deleteComment: handleRequest(storyService.deleteComment),
  getFollowersStories: handleRequest(storyService.getFollowersStories, true)
}; 