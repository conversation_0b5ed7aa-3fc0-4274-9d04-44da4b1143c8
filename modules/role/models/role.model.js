'use strict';

module.exports = (sequelize, DataTypes) => {
  const Role = sequelize.define('Role', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    roleDisplayName: {
      type: DataTypes.STRING,
      defaultValue: ''
    },
    role: {
      type: DataTypes.STRING,
      defaultValue: 'user'
    },
    desc: {
      type: DataTypes.STRING,
      defaultValue: ''
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
  }, {
    timestamps: true,
    tableName: 'Roles'
  });

  Role.associate = (models) => {
    Role.hasMany(models.User, { 
      foreignKey: 'role',
      as: 'users',
      sourceKey: 'id'
    });
  };
  return Role;
};