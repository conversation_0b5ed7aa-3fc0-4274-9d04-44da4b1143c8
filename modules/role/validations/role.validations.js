const Joi = require("joi");

module.exports.addRole = {
    body: Joi.object().keys({
        roleDisplayName: Joi.string().optional().allow(null, ""),
        role: Joi.string().optional().allow(null, ""),
        desc: Joi.string().optional().allow(null, "")
    }),
};
module.exports.getRoles = {
    body: Joi.object().keys({
        filters: Joi.object().required(),
        page: Joi.number().required(),
        limit: Joi.number().required()
    }),
};