const { catchAsync } = require("../../../utils/catchAsync");
// const { roleService, tokenService } = require("../services");
const { roleService } = require("../../../allservice")
const httpStatus = require("http-status");



const {handleRequest} = require("../../../helpers/handleRequest")
  module.exports.addRole = handleRequest(roleService.addRole);
  module.exports.getRoles = handleRequest(roleService.getRoles);
 