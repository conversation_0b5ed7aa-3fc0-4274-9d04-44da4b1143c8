const {Role} = require("../../../models")
const httpStatus = require("http-status");
const ApiError = require("../../../utils/ApiError");
const User = require("../../user/models/user.model");
// const activityService = require("../../activity/services/activity.service");
// const { _isObjectId } = require("../../../helpers/global.functions");
// const  mongoose = require("mongoose");

const addRole = async(reqBody) =>{
 const role = await Role.create(reqBody)
 if(reqBody.createdBy){
    // await activityService.addActivity({actionOn: reqBody.createdBy, actionBy: reqBody.createdBy, actionName: "addRole" , actionDescription: `role name ${reqBody.roleDisplayName}` })
 }
 return role
}

const getRoles = async ({ filters, page, limit }, user) => {
    // console.log(user);

    // Clean and transform filters
    Object.entries(filters).forEach(([key, value]) => {
        if (value === "") delete filters[key];
    });

    // Define pagination options
    const offset = (page - 1) * limit;

    // Fetch roles using Sequelize
    const roleDocs = await Role.findAndCountAll({
        where: filters,
        attributes: ["roleDisplayName", "role", "desc"],
        limit,
        offset,
    });

    return {
        total: roleDocs.count,
        roles: roleDocs.rows,
        page,
        limit
    };
};

module.exports={
    addRole,
    getRoles
}