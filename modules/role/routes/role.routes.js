const express = require("express");
const router = express.Router();
const validate = require("../../../helpers/validate");
const  roleValidation  = require("../validations/role.validations");
const roleController = require("../controllers/role.controller");
const multer = require('multer');
const { storage } = require("../../../configuration/storage");
const { authorize } = require("../../../authwires/auth");
const upload = multer({ storage: storage });


/**
 * @swagger
 * /role/add-role:
 *   post:
 *     summary: add role
 *     tags:
 *       - Role
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               roleDisplayName:
 *                 type: string
 *                 description: Display name for the role
 *               role:
 *                 type: string
 *                 description: Role of the user
 *               desc:
 *                 type: string
 *                 description: Description of the role
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/add-role', validate(roleValidation.addRole), roleController.addRole)



// authorize routes
// router.use(authorize)



/**
 * @swagger
 * /role/get-roles:
 *   post:
 *     summary: fetch color preferences
 *     tags:
 *       - Role
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               filters:
 *                 type: object
 *                 description: role name
 *               page:
 *                type: number
 *               limit:
 *                type: number
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/get-roles', validate(roleValidation.getRoles), roleController.getRoles)
// router.post('/get-role', validate(roleValidation.getRole), roleController.getRole)
// router.post('/delete-role', validate(roleValidation.deleteRole), roleController.deleteRole)


module.exports = router;
