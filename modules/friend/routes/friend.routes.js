const express = require("express");
const router = express.Router();
const validate = require("../../../helpers/validate");
const  friendValidation  = require("../validations/friend.validations");
const friendController = require("../controllers/friend.controller");
const multer = require('multer');
const { storage } = require("../../../configuration/storage");
const { authorize } = require("../../../authwires/auth");
const upload = multer({ storage: storage });


router.use(authorize)

/**
 * @swagger
 * /friend/add-friend:
 *   post:
 *     summary: add friend
 *     tags:
 *       - Friend
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               status:
 *                 type: string
 *                 description: Display name for the friend
 *               description:
 *                 type: string
 *               user_id_2:
 *                 type: number
 *                 description: 2nd friend
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/add-friend', validate(friendValidation.addFriend), friendController.addFriend)
/**
 * @swagger
 * /friend/edit-friend:
 *   post:
 *     summary: edit friend
 *     tags:
 *       - Friend
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               friend_id:
 *                 type: number
 *               user_id_2:
 *                 type: number
 *                 description: 2nd friend
 *               user_id_1:
 *                 type: number
 *                 description: Display name for the friend
 *               status:
 *                 type: string
 *                 description: Friend of the user
 *               is_deleted:
 *                 type: boolean
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/edit-friend', validate(friendValidation.editFriend), friendController.editFriend)



// authorize routes
// router.use(authorize)



/**
 * @swagger
 * /friend/get-friends:
 *   post:
 *     summary: fetch color preferences
 *     tags:
 *       - Friend
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               filters:
 *                 type: object
 *                 description: friend name
 *                 properties:
 *                   user_id_2:
 *                     type: number
 *                     description: your user id
 *                   status:
 *                     type: string
 *                     enum: [pending , accepted , rejected]
 *               page:
 *                type: number
 *               limit:
 *                type: number
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/get-friends', validate(friendValidation.getFriends), friendController.getFriends)
// router.post('/get-friend', validate(friendValidation.getFriend), friendController.getFriend)
// router.post('/delete-friend', validate(friendValidation.deleteFriend), friendController.deleteFriend)

router.get('/pending-requests', friendController.getPendingRequests);

module.exports = router;
