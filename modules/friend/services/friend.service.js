const Friend = require("../models/friend.model");
// const User = require("../../user/models/user.model");
const {User} = require("../../../models")
const httpStatus = require("http-status");
const ApiError = require("../../../utils/ApiError");
const { getSession } = require('../../../configuration/neo4j-config');
const logger = require('../../../utils/logger');
// const User = require("../../user/models/user.model");
// const activityService = require("../../activity/services/activity.service");
// const { _isObjectId } = require("../../../helpers/global.functions");
// const  mongoose = require("mongoose");
const { Op, Sequelize } = require("sequelize");

const addFriend = async (reqBody, user) => {
  const session = getSession();
  try {
    logger.info(`Adding friend: user ${user.id} -> ${reqBody.user_id} with status ${reqBody.status}`);

    // Check if target user exists in Neo4j
    const targetUserResult = await session.run(
      'MATCH (u:User {id: $userId}) RETURN u',
      { userId: reqBody.user_id }
    ).catch(error => {
      logger.error('Neo4j query error:', error);
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Database error while checking user');
    });

    if (!targetUserResult.records.length) {
      logger.info(`Creating new user node in Neo4j for user ${reqBody.user_id}`);
      // If user doesn't exist in Neo4j, create the node
      await session.run(
        'CREATE (u:User {id: $userId})',
        { userId: reqBody.user_id }
      ).catch(error => {
        logger.error('Neo4j create error:', error);
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Database error while creating user node');
      });
    }

    // Check if friendship exists
    if (reqBody.status === "pending") {
      const existingFriendship = await Friend.getFriendshipStatus(user.id, reqBody.user_id)
        .catch(error => {
          logger.error('Error checking friendship status:', error);
          throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error checking friendship status');
        });

      if (existingFriendship) {
        throw new ApiError(httpStatus.BAD_REQUEST, `User already ${existingFriendship.status}`);
      }
    }

    // Check for blocked users
    const blockedUsers = await Friend.getBlockedUsers(user.id)
      .catch(error => {
        logger.error('Error getting blocked users:', error);
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error checking blocked users');
      });

    if (blockedUsers.includes(reqBody.user_id) && reqBody.status !== "blocked" && reqBody.status !== "unblock") {
      throw new ApiError(httpStatus.BAD_REQUEST, "User is blocked! action not permitted");
    }

    let status = reqBody.status;
    if (status === "unblock") {
      status = "deleted";
    }

    // Handle friend request
    if (status === "pending") {
      // If target user's privacy is public, automatically accept the request
      const targetUser = await User.findByPk(reqBody.user_id)
        .catch(error => {
          logger.error('Error finding target user:', error);
          throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error checking user privacy settings');
        });

      if (targetUser && targetUser.privacy_status === "public") {
        status = "accepted";
      }
    }

    // Create or update friendship with all required properties
    const timestamp = new Date().toISOString();
    if (status === "deleted") {
      await session.run(
        `
        MATCH (user1:User {id: $userId1})-[f:FRIENDS_WITH]-(user2:User {id: $userId2})
        SET f.is_deleted = true,
            f.updated_at = $timestamp
        `,
        { 
          userId1: user.id, 
          userId2: reqBody.user_id,
          timestamp 
        }
      ).catch(error => {
        logger.error('Error deleting friendship:', error);
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error deleting friendship');
      });
    } else {
      await session.run(
        `
        MERGE (user1:User {id: $userId1})
        MERGE (user2:User {id: $userId2})
        MERGE (user1)-[f:FRIENDS_WITH]->(user2)
        SET f.status = $status,
            f.is_deleted = false,
            f.created_at = CASE WHEN f.created_at IS NULL THEN $timestamp ELSE f.created_at END,
            f.updated_at = $timestamp
        `,
        { 
          userId1: user.id, 
          userId2: reqBody.user_id,
          status,
          timestamp 
        }
      ).catch(error => {
        logger.error('Error creating friendship:', error);
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error creating friendship');
      });
    }

    // Get the complete friendship data including user details
    const result = await session.run(
      `
      MATCH (user1:User {id: $userId1})-[f:FRIENDS_WITH]->(user2:User {id: $userId2})
      WHERE f.is_deleted = false
      RETURN f, user1, user2
      `,
      { userId1: user.id, userId2: reqBody.user_id }
    );

    const friendship = result.records[0];
    if (!friendship) {
      // If we can't find the friendship, return a simpler response
      return {
        message: "Friend request processed successfully",
        status: status,
        from_user_id: user.id,
        to_user_id: reqBody.user_id
      };
    }

    return {
      message: "Friend request processed successfully",
      friendship: {
        status: friendship.get('f').properties.status,
        created_at: friendship.get('f').properties.created_at,
        updated_at: friendship.get('f').properties.updated_at,
        is_deleted: friendship.get('f').properties.is_deleted,
        from_user: {
          id: friendship.get('user1').properties.id,
          username: friendship.get('user1').properties.username
        },
        to_user: {
          id: friendship.get('user2').properties.id,
          username: friendship.get('user2').properties.username
        }
      }
    };
  } catch (error) {
    logger.error('Friend service error:', error);
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error processing friend request');
  } finally {
    await session.close();
  }
};

const editFriend = async (reqBody) => {
  try {
    const friendship = await Friend.getFriendshipStatus(reqBody.user_id_1, reqBody.user_id_2)
      .catch(error => {
        logger.error('Error getting friendship status:', error);
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error checking friendship status');
      });

    if (!friendship) {
      throw new ApiError(httpStatus.NOT_FOUND, "Friendship not found");
    }

    await Friend.updateFriendshipStatus(reqBody.user_id_1, reqBody.user_id_2, reqBody.status)
      .catch(error => {
        logger.error('Error updating friendship status:', error);
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error updating friendship status');
      });

    return await Friend.getFriendshipStatus(reqBody.user_id_1, reqBody.user_id_2)
      .catch(error => {
        logger.error('Error getting updated friendship status:', error);
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error retrieving updated friendship status');
      });
  } catch (error) {
    logger.error('Edit friend error:', error);
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error editing friendship');
  }
};

const getFriends = async ({ filters, page, limit }, user) => {
  const session = getSession();
  try {
    logger.info(`Getting friends for user ${filters.user_id || user.id} with status ${filters.status || 'all'}`);
    const skip = Math.floor((page - 1) * limit);
    const limitInt = Math.floor(limit);
    const userId = filters.user_id || user.id;

    // Get friends with all required information
    const result = await session.run(
      `
      MATCH (user1:User {id: $userId})-[f:FRIENDS_WITH]-(user2:User)
      WHERE f.is_deleted = false
      ${filters.status ? 'AND f.status = $status' : ''}
      WITH user1, user2, f
      OPTIONAL MATCH (user1)-[:FOLLOWS]->(user2)
      WITH user1, user2, f, count(user2) as isFollowed
      OPTIONAL MATCH (user1)-[:FOLLOWS]->(mutual:User)<-[:FOLLOWS]-(user2)
      WITH user1, user2, f, isFollowed, count(mutual) as mutualFollowersCount
      OPTIONAL MATCH (user1)<-[:FOLLOWS]-(follower1:User)
      WITH user1, user2, f, isFollowed, mutualFollowersCount, count(follower1) as user1FollowersCount
      OPTIONAL MATCH (user1)-[:FOLLOWS]->(following1:User)
      WITH user1, user2, f, isFollowed, mutualFollowersCount, user1FollowersCount, count(following1) as user1FollowingCount
      OPTIONAL MATCH (user2)<-[:FOLLOWS]-(follower2:User)
      WITH user1, user2, f, isFollowed, mutualFollowersCount, user1FollowersCount, user1FollowingCount, count(follower2) as user2FollowersCount
      OPTIONAL MATCH (user2)-[:FOLLOWS]->(following2:User)
      RETURN user1.id as user1Id, user2.id as user2Id, f,
             isFollowed > 0 as isFollowed,
             mutualFollowersCount,
             user1FollowersCount,
             user1FollowingCount,
             user2FollowersCount,
             count(following2) as user2FollowingCount
      ORDER BY f.created_at DESC
      SKIP toInteger($skip)
      LIMIT toInteger($limit)
      `,
      { 
        userId, 
        status: filters.status,
        skip, 
        limit: limitInt 
      }
    );

    logger.info(`Query returned ${result.records.length} records`);

    // Get user details from PostgreSQL
    const userIds = result.records.flatMap(record => [
      record.get('user1Id'),
      record.get('user2Id')
    ]);
    
    const users = await User.findAll({
      where: {
        id: userIds
      },
      attributes: ['id', 'username', 'first_name', 'last_name', 'profile_picture_url']
    });

    const userMap = users.reduce((acc, user) => {
      acc[user.id] = user;
      return acc;
    }, {});

    const friends = result.records.map(record => {
      const user1Id = record.get('user1Id');
      const user2Id = record.get('user2Id');
      const friendship = record.get('f').properties;
      
      const user1 = userMap[user1Id];
      const user2 = userMap[user2Id];
      
      // Determine which user is the friend based on the current user's ID
      const isUser1CurrentUser = user1Id === userId;
      
      return {
        id: friendship.id || `${user1Id}-${user2Id}`,
        user_id_1: user1Id,
        user_id_2: user2Id,
        status: friendship.status,
        isFollowed: record.get('isFollowed'),
        myStatus: isUser1CurrentUser ? friendship.status : null,
        friendStatus: !isUser1CurrentUser ? friendship.status : null,
        mutualFollowersCount: record.get('mutualFollowersCount').toString(),
        user1FollowersCount: record.get('user1FollowersCount').toString(),
        user1FollowingCount: record.get('user1FollowingCount').toString(),
        user2FollowersCount: record.get('user2FollowersCount').toString(),
        user2FolloweingCount: record.get('user2FollowingCount').toString(),
        User1: {
          id: user1.id,
          username: user1.username || '',
          first_name: user1.first_name || '',
          last_name: user1.last_name || '',
          profile_picture_url: user1.profile_picture_url
        },
        User2: {
          id: user2.id,
          username: user2.username || '',
          first_name: user2.first_name || '',
          last_name: user2.last_name || '',
          profile_picture_url: user2.profile_picture_url
        }
      };
    });

    // Get total count for pagination
    const countResult = await session.run(
      `
      MATCH (user:User {id: $userId})-[f:FRIENDS_WITH]-(friend:User)
      WHERE f.is_deleted = false
      ${filters.status ? 'AND f.status = $status' : ''}
      RETURN count(DISTINCT friend) as total
      `,
      { 
        userId, 
        status: filters.status
      }
    );

    const total = countResult.records[0]?.get('total') || 0;
    logger.info(`Total friends count: ${total}`);

    return {
      total,
      friends,
      page,
      limit: limitInt
    };
  } catch (error) {
    logger.error('Get friends error:', error);
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error retrieving friends list');
  } finally {
    await session.close();
  }
};

const getPendingRequests = async (userId) => {
  const session = getSession();
  try {
    logger.info(`Getting pending friend requests for user ${userId}`);
    
    const result = await session.run(
      `
      MATCH (user:User {id: $userId})<-[f:FRIENDS_WITH]-(requester:User)
      WHERE f.status = 'pending' AND f.is_deleted = false
      RETURN requester, f.created_at as requestTime
      ORDER BY f.created_at DESC
      `,
      { userId }
    );

    return result.records.map(record => ({
      ...record.get('requester').properties,
      requestTime: record.get('requestTime')
    }));
  } catch (error) {
    logger.error('Error getting pending requests:', error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error retrieving pending friend requests');
  } finally {
    await session.close();
  }
};

module.exports = {
  addFriend,
  getFriends,
  editFriend,
  getPendingRequests
};
