'use strict';

const { getSession } = require('../../../configuration/neo4j-config');

class Friend {
    static async createFriendship(userId1, userId2, status = 'pending') {
        const session = getSession();
        try {
            const result = await session.run(
                `
                MATCH (user1:User {id: $userId1})
                MATCH (user2:User {id: $userId2})
                MERGE (user1)-[f:FRIENDS_WITH {
                    status: $status,
                    created_at: datetime(),
                    updated_at: datetime(),
                    is_deleted: false
                }]->(user2)
                RETURN f
                `,
                { userId1, userId2, status }
            );
            return result.records[0]?.get('f').properties;
        } finally {
            await session.close();
        }
    }

    static async updateFriendshipStatus(userId1, userId2, status) {
        const session = getSession();
        try {
            const result = await session.run(
                `
                MATCH (user1:User {id: $userId1})-[f:FRIENDS_WITH]->(user2:User {id: $userId2})
                SET f.status = $status,
                    f.updated_at = datetime()
                RETURN f
                `,
                { userId1, userId2, status }
            );
            return result.records[0]?.get('f').properties;
        } finally {
            await session.close();
        }
    }

    static async deleteFriendship(userId1, userId2) {
        const session = getSession();
        try {
            const result = await session.run(
                `
                MATCH (user1:User {id: $userId1})-[f:FRIENDS_WITH]->(user2:User {id: $userId2})
                SET f.is_deleted = true,
                    f.updated_at = datetime()
                RETURN f
                `,
                { userId1, userId2 }
            );
            return result.records[0]?.get('f').properties;
        } finally {
            await session.close();
        }
    }

    static async getFriendshipStatus(userId1, userId2) {
        const session = getSession();
        try {
            const result = await session.run(
                `
                MATCH (user1:User {id: $userId1})-[f:FRIENDS_WITH]->(user2:User {id: $userId2})
                WHERE f.is_deleted = false
                RETURN f
                `,
                { userId1, userId2 }
            );
            return result.records[0]?.get('f').properties;
        } finally {
            await session.close();
        }
    }

    static async getBlockedUsers(userId) {
        const session = getSession();
        try {
            const result = await session.run(
                `
                MATCH (user:User {id: $userId})-[f:FRIENDS_WITH]->(blocked:User)
                WHERE f.status = 'blocked' AND f.is_deleted = false
                RETURN blocked.id as blockedId
                `,
                { userId }
            );
            return result.records.map(record => record.get('blockedId'));
        } finally {
            await session.close();
        }
    }

    static async getFriends(userId, skip = 0, limit = 10) {
        const session = getSession();
        try {
            const result = await session.run(
                `
                MATCH (user:User {id: $userId})-[f:FRIENDS_WITH]->(friend:User)
                WHERE f.is_deleted = false AND f.status = 'accepted'
                RETURN friend, f.status as status
                SKIP $skip
                LIMIT $limit
                `,
                { userId, skip, limit }
            );
            return result.records.map(record => ({
                ...record.get('friend').properties,
                status: record.get('status')
            }));
        } finally {
            await session.close();
        }
    }

    static async getFollowers(userId, skip = 0, limit = 10) {
        const session = getSession();
        try {
            const result = await session.run(
                `
                MATCH (user:User {id: $userId})<-[f:FRIENDS_WITH]-(follower:User)
                WHERE f.is_deleted = false AND f.status = 'accepted'
                RETURN follower, f.status as status
                SKIP $skip
                LIMIT $limit
                `,
                { userId, skip, limit }
            );
            return result.records.map(record => ({
                ...record.get('follower').properties,
                status: record.get('status')
            }));
        } finally {
            await session.close();
        }
    }

    static async getFollowing(userId, skip = 0, limit = 10) {
        const session = getSession();
        try {
            const result = await session.run(
                `
                MATCH (user:User {id: $userId})-[f:FRIENDS_WITH]->(following:User)
                WHERE f.is_deleted = false AND f.status = 'accepted'
                RETURN following, f.status as status
                SKIP $skip
                LIMIT $limit
                `,
                { userId, skip, limit }
            );
            return result.records.map(record => ({
                ...record.get('following').properties,
                status: record.get('status')
            }));
        } finally {
            await session.close();
        }
    }

    static async getFriendsWithStatus(userId, status, skip = 0, limit = 10) {
        const session = getSession();
        try {
            // Convert skip and limit to integers
            const skipInt = Math.floor(skip);
            const limitInt = Math.floor(limit);
            
            const result = await session.run(
                `
                MATCH (user:User {id: $userId})-[f:FRIENDS_WITH]->(friend:User)
                WHERE f.is_deleted = false
                ${status ? 'AND f.status = $status' : ''}
                RETURN friend, f.status as status
                SKIP $skip
                LIMIT $limit
                `,
                { userId, status, skip: skipInt, limit: limitInt }
            );
            return result.records.map(record => ({
                ...record.get('friend').properties,
                status: record.get('status')
            }));
        } finally {
            await session.close();
        }
    }
    
    static async getFollowersCount(userId) {
        const session = getSession();
        try {
            const result = await session.run(
                `
                MATCH (user:User {id: $userId})<-[f:FRIENDS_WITH]-(follower:User)
                WHERE f.is_deleted = false AND f.status = 'accepted'
                RETURN count(follower) as count
                `,
                { userId }
            );
            return result.records[0]?.get('count') || 0;
        } finally {
            await session.close();
        }
    }

    static async getFriendsCount(userId, query = {}) {
        const session = getSession();
        try {
            const result = await session.run(
                `
                MATCH (user:User {id: $userId})-[f:FRIENDS_WITH]->(friend:User)
                WHERE f.is_deleted = false
                ${query.status ? 'AND f.status = $status' : ''}
                RETURN count(friend) as count
                `,
                { userId, status: query.status }
            );
            return result.records[0]?.get('count').toNumber() || 0;
        } finally {
            await session.close();
        }
    }
}

module.exports = Friend;