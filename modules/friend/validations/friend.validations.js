const Joi = require("joi");

module.exports.addFriend = {
    body: Joi.object().keys({
        user_id: Joi.number().required(),
        status: Joi.string().valid("pending", "accepted", "rejected", "blocked", "unblock", "deleted").required()
    }),
};

module.exports.editFriend = {
    body: Joi.object().keys({
        friend_id: Joi.number().required(),
        status: Joi.string().optional().allow(null, ""),
        user_id_1: Joi.number().optional().allow(null , ""),
        user_id_2: Joi.number().optional().allow(null , ""),
        is_deleted: Joi.boolean().optional().allow(null, "")
    }),
};

module.exports.getFriends = {
    body: Joi.object().keys({
        filters: Joi.object().required(),
        page: Joi.number().required(),
        limit: Joi.number().required()
    }),
};