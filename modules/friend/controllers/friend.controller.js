const { catchAsync } = require("../../../utils/catchAsync");
// const { friendService, tokenService } = require("../services");
// const { friendService } = require("../../../allservice")
const friendService = require("../services/friend.service")
const httpStatus = require("http-status");
// const { handleRequest } = require("../../../helpers/request.handler");




const {handleRequest} = require("../../../helpers/handleRequest")
  module.exports.addFriend = handleRequest(friendService.addFriend);
  module.exports.editFriend = handleRequest(friendService.editFriend);
  module.exports.getFriends = handleRequest(friendService.getFriends);
  module.exports.getPendingRequests = handleRequest(friendService.getPendingRequests);
 