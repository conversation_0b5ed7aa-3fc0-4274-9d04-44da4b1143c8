const httpStatus = require("http-status");
const { catchAsync } = require("../../../utils/catchAsync");
const awsService = require("../services/aws.service")

// const { awsService } = require("../services");
module.exports.generatePresignedUrl = catchAsync(async (req, res) => {
  const directory = await awsService.generatePresignedUrl(req.body);
  res.status(httpStatus.OK).json({ success: true, directory });
});