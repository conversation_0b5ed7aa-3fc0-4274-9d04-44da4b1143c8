const { S3Client, PutObjectCommand, DeleteObjectCommand } = require('@aws-sdk/client-s3');
const ApiError = require("../../../utils/ApiError");
const httpStatus = require('http-status');
const multer = require('multer');
const fs = require('fs');

const s3 = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  }
});

const uploadToS3 = async (file, key) => {
    const allowedMimes = ['image/jpeg', 'image/png', 'image/gif' ,'video/mov', "video/mp4", "video/quicktime"];
    if (!allowedMimes.includes(file.mimetype)) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid file type. Only JPEG, PNG, GIF and MP4 are allowed.');
    }

    const fileBuffer = fs.readFileSync(file.path);
const params = {
    Bucket: process.env.S3_BUCKET_NAME,
    Key: key,
    Body: fileBuffer,
    ContentType: file.mimetype
};

    if (file.size > 10 * 1024 * 1024) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'File size exceeds 10MB limit');
    }

    try {
        await s3.send(new PutObjectCommand(params));
        const url = `https://${process.env.S3_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;
fs.unlinkSync(file.path);
return url;
    } catch (error) {
        console.error('S3 Upload Error:', error);
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error uploading file to S3');
    }
};

const deleteFromS3 = async (fileUrl) => {
    try {
        const key = fileUrl?.split('.amazonaws.com/')[1] || fileUrl;
        await s3.send(new DeleteObjectCommand({
            Bucket: process.env.S3_BUCKET_NAME,
            Key: key
        }));
        return true;
    } catch (error) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error deleting file from S3');
    }
};

// Configure multer with S3 storage
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'uploads/')
    },
    filename: function (req, file, cb) {
        cb(null, `${Date.now()}-${file.originalname}`)
    }
});

// File filter function
const fileFilter = (req, file, cb) => {
    const allowedMimes = ['image/jpeg', 'image/png', 'image/gif'];
    if (allowedMimes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new ApiError(httpStatus.BAD_REQUEST, 'Invalid file type. Only JPEG, PNG and GIF are allowed.'), false);
    }
};

// Configure multer middleware
const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    }
});

module.exports = {
    uploadToS3,
    deleteFromS3,
    upload
};