const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
const ApiError = require("../../../utils/ApiError");
const httpStatus = require('http-status');

const s3 = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  }
});

const generatePresignedUrl = async (reqBody) => {
  const { files, type } = reqBody;
  
  if (!files || !Array.isArray(files) || files.length === 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Files array is required");
  }

  // if (files.length > 5) {
  //   throw new ApiError(httpStatus.BAD_REQUEST, "Maximum 5 files allowed");
  // }

  const urls = await Promise.all(
    files.map(async (file) => {
      const fileType = file.fileType || file.mimetype;
      let fileName = file.fileName || file.originalname;
      
      if (!fileType || !fileName) {
        throw new ApiError(httpStatus.BAD_REQUEST, "File type and name are required");
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'video/mp4' , 'video/mov' , "video/quicktime"];
      if (!allowedTypes.includes(fileType)) {
        throw new ApiError(httpStatus.BAD_REQUEST, "Invalid file type. Only JPEG, PNG, GIF, MP4 and MOV are allowed");
      }
      fileName = `${Date.now()}_${fileName}`
      const key = `uploads/${fileName}`;
      const command = new PutObjectCommand({
        Bucket: process.env.S3_BUCKET_NAME,
        Key: key,
        ContentType: fileType
      });

      const presignedUrl = await getSignedUrl(s3, command, { expiresIn: 300 });
      const fileUrl = `https://d2p8o1syjdr4y0.cloudfront.net/${key}`;

      return {
        fileName,
        fileType,
        presignedUrl,
        fileUrl,
        key
      };
    })
  );

  return urls;
};

module.exports = {
  generatePresignedUrl
};