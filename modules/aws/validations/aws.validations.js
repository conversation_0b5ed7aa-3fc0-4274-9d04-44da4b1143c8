const Joi = require("joi")

module.exports.generatePresignedUrl = {
    body: Joi.object().keys({
        files: Joi.array().items(
            Joi.object().keys({
                fileName: Joi.string().required(),
                fileType: Joi.string().required().valid('image/jpeg', 'image/png', 'image/gif', 'video/mp4' , 'video/mov' , "video/quicktime")
            })
        ).min(1).required(),
        type: Joi.string().optional().valid('profile-pictures', 'cover-pictures', 'posts', 'uploads')
    }),
};