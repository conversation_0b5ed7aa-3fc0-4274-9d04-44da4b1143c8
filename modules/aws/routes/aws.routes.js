

const express = require("express");
const router = express.Router();
const validate = require("../../../helpers/validate");
// const { awsValidation } = require("../validations");
// const { awsController } = require("../controllers");
const  awsValidation  = require("../validations/aws.validations");
const awsController = require("../controllers/aws.controller");
const multer = require("multer");
const upload = multer({ dest: "images/" });
const { authorize } = require("../../../authwires/auth");

// router.use(authorize)
/**
 * @swagger
 * /aws/generate-presigned-url:
 *   post:
 *     summary: Generate a pre-signed URL
 *     tags:
 *       - AWS
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               files:
 *                 type: array
 *                 description: Display name for the friend
 *                 items:
 *                  type: object
 *                  properties:
 *                    fileName:
 *                      type: string
 *                    fileType:
 *                      type: string
 *     responses:
 *       200:
 *         description: Pre-signed URL generated successfully.
 *       400:
 *         description: Bad Request
 *       500:
 *         description: Server Error
 */

router.post("/generate-presigned-url", validate(awsValidation.generatePresignedUrl), awsController.generatePresignedUrl)

module.exports = router;


