const {Group_member, Follower, User , Reportgroup} = require("../../../models")
const Friend = require("../../friend/models/friend.model");
const httpStatus = require("http-status");
const ApiError = require("../../../utils/ApiError");
const { Op } = require('sequelize');
const { Sequelize } = require('sequelize');
// const User = require("../../user/models/user.model");
// const activityService = require("../../activity/services/activity.service");
// const { _isObjectId } = require("../../../helpers/global.functions");
// const  mongoose = require("mongoose");

const addGroup_member = async(reqBody) =>{
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }

    const reportGroup = await Reportgroup.findOne({where:{group_id: reqBody.group_id , user_id: reqBody.user_id}})

    if(reportGroup) throw new ApiError(httpStatus.BAD_REQUEST, "this user already reported to the group can not be added")
    // Check if user is already a member of the group
    const existingMember = await Group_member.findOne({
        where: {
            group_id: reqBody.group_id,
            user_id: reqBody.user_id,
            is_deleted: false,
            status: {[Op.not]: 'rejected'}
        }
    });

    if (existingMember) {
        throw new ApiError(
            httpStatus.BAD_REQUEST, 
            "User is already a member of this group or has a pending request"
        );
    }

    const group_member = await Group_member.create(reqBody)
    if(reqBody.createdBy){
        // await activityService.addActivity({actionOn: reqBody.createdBy, actionBy: reqBody.createdBy, actionName: "addGroup_member" , actionDescription: `group_member name ${reqBody.group_memberDisplayName}` })
    }
    return group_member
}
const editGroup_member = async(reqBody) =>{
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }
    const [updatedCount, updatedGroup_members] = await Group_member.update(reqBody, {
        where: { id: reqBody.group_member_id },
        returning: true,
    });

    if (updatedCount === 0) {
        throw new ApiError(httpStatus.NOT_FOUND, "Group_member not found");
    }
    return updatedGroup_members[0];
}

const getGroup_members = async ({ filters, page, limit }, user) => {
    // Clean and transform filters
    Object.entries(filters).forEach(([key, value]) => {
        if (value === "") delete filters[key];
    });

    // Define pagination options
    const offset = (page - 1) * limit;

    // Build where clause for search
    let whereClause = { ...filters };
    if (filters.search) {
        delete whereClause.search;
    }

    // Fetch group_members using Sequelize
    const group_memberDocs = await Group_member.findAndCountAll({
        include: [
            {
                model: User,
                as: 'user',
                attributes: [
                    "id", 
                    "username", 
                    "email", 
                    "profile_picture_url",
                    "first_name"
                ],
                where: filters.search ? {
                    [Op.or]: [
                        { username: { [Op.iLike]: `%${filters.search}%` } },
                        { first_name: { [Op.iLike]: `%${filters.search}%` } }
                    ]
                } : {}
            }
        ],
        where: whereClause,
        limit,
        offset,
    });

    // Get friend statuses for all members using Neo4j
    const groupMembers = await Promise.all(group_memberDocs.rows.map(async (member) => {
        const userData = member.user.toJSON();
        
        // Get friend status using Neo4j
        const myStatus = await Friend.getFriendshipStatus(user.id, userData.id);
        const friendStatus = await Friend.getFriendshipStatus(userData.id, user.id);

        return {
            ...member.toJSON(),
            user: {
                ...userData,
                myStatus: myStatus?.status || null,
                friendStatus: friendStatus?.status || null
            }
        };
    }));

    return {
        total: group_memberDocs.count,
        group_members: groupMembers,
        page,
        limit
    };
};

const deleteGroup_memberByUserId = async ({group_id , user_id}, user) =>{

    let group_member = await Group_member.findOne({where:{user_id , group_id}})
    if(group_member) await group_member.update({is_deleted: true})
}

module.exports={
    addGroup_member,
    getGroup_members,
    editGroup_member,
    deleteGroup_memberByUserId
}