const { catchAsync } = require("../../../utils/catchAsync");
// const { group_memberService, tokenService } = require("../services");
// const { group_memberService } = require("../../../allservice")
const group_memberService = require("../services/group_member.service")
const httpStatus = require("http-status");



const {handleRequest} = require("../../../helpers/handleRequest")
  module.exports.addGroup_member = handleRequest(group_memberService.addGroup_member);
  module.exports.editGroup_member = handleRequest(group_memberService.editGroup_member);
  module.exports.getGroup_members = handleRequest(group_memberService.getGroup_members);
  module.exports.deleteGroup_memberByUserId = handleRequest(group_memberService.deleteGroup_memberByUserId);
 