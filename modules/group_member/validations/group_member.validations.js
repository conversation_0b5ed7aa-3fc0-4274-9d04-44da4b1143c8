const Joi = require("joi");

module.exports.addGroup_member = {
    body: Joi.object().keys({
        group_id: Joi.number().required(),
        user_id: Joi.number().required(),
        role: Joi.string().required()
    }),
};
module.exports.editGroup_member = {
    body: Joi.object().keys({
        group_member_id: Joi.number().required(),
        group_id: Joi.number().optional().allow(null, ""),
        user_id: Joi.number().optional().allow(null, ""),
        role: Joi.string().optional().allow(null, ""),
        is_deleted: Joi.boolean().optional().allow(null, "")
    }),
};
module.exports.getGroup_members = {
    body: Joi.object().keys({
        filters: Joi.object().required(),
        page: Joi.number().required(),
        limit: Joi.number().required()
    }),
};
module.exports.deleteGroup_memberByUserId = {
    body: Joi.object().keys({
        user_id: Joi.number().required(),
        group_id: Joi.number().required()
    }),
};