'use strict';

module.exports = (sequelize, DataTypes) => {
  const Group_member = sequelize.define('Group_member', {
    group_id:{
      type: DataTypes.INTEGER
    },
    user_id:{
      type: DataTypes.INTEGER
    },
    role:{
      type: DataTypes.STRING
    },
    status: {
      type: DataTypes.ENUM('pending', 'accepted', 'rejected'),
      defaultValue: 'pending'
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    created_at:{
      type: DataTypes.DATE
    },
    updated_at:{
      type: DataTypes.DATE
    },
  }, {
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });

  Group_member.associate = (models) => {
    Group_member.belongsTo(models.User, { foreignKey: 'user_id', as: 'user' });
    Group_member.belongsTo(models.Group, { foreignKey: 'group_id', as: 'group' });
  };
  return Group_member;
};