const express = require("express");
const router = express.Router();
const validate = require("../../../helpers/validate");
const  group_memberValidation  = require("../validations/group_member.validations");
const group_memberController = require("../controllers/group_member.controller");
const multer = require('multer');
const { storage } = require("../../../configuration/storage");
const { authorize } = require("../../../authwires/auth");
const upload = multer({ storage: storage });


router.use(authorize)

/**
 * @swagger
 * /group_member/add-group_member:
 *   post:
 *     summary: add group_member
 *     tags:
 *       - Group_member
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               group_id:
 *                 type: number
 *                 description: Display name for the group_member
 *               user_id:
 *                 type: number
 *                 description: Display name for the group_member
 *               role:
 *                 type: string
 *                 description: Group_member of the user
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/add-group_member', validate(group_memberValidation.addGroup_member), group_memberController.addGroup_member)
/**
 * @swagger
 * /group_member/edit-group_member:
 *   post:
 *     summary: edit group_member
 *     tags:
 *       - Group_member
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               group_member_id:
 *                 type: number
 *               group_id:
 *                 type: number
 *                 description: Display name for the group_member
 *               user_id:
 *                 type: number
 *                 description: Display name for the group_member
 *               role:
 *                 type: string
 *                 description: Group_member of the user
 *               is_deleted:
 *                 type: boolean
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/edit-group_member', validate(group_memberValidation.editGroup_member), group_memberController.editGroup_member)



// authorize routes
// router.use(authorize)



/**
 * @swagger
 * /group_member/get-group_members:
 *   post:
 *     summary: fetch color preferences
 *     tags:
 *       - Group_member
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               filters:
 *                 type: object
 *                 description: group_member name
 *               page:
 *                type: number
 *               limit:
 *                type: number
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/get-group_members', validate(group_memberValidation.getGroup_members), group_memberController.getGroup_members)


/**
 * @swagger
 * /group_member/delete-group_member-by-userid':
 *   post:
 *     summary: delete group_member
 *     tags:
 *       - Group_member
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               group_id:
 *                 type: number
 *                 description: Display name for the group_member
 *               user_id:
 *                 type: number
 *                 description: Display name for the group_member
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/delete-group_member-by-userid', validate(group_memberValidation.deleteGroup_memberByUserId), group_memberController.deleteGroup_memberByUserId)

// router.post('/get-group_member', validate(group_memberValidation.getGroup_member), group_memberController.getGroup_member)
// router.post('/delete-group_member', validate(group_memberValidation.deleteGroup_member), group_memberController.deleteGroup_member)


module.exports = router;
