'use strict';

module.exports = (sequelize, DataTypes) => {
  const Reportgroup = sequelize.define('Reportgroup', {
    group_id:{
      type: DataTypes.INTEGER
    },
    user_id:{
      type: DataTypes.INTEGER
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
  }, {
    timestamps: true
  });

  Reportgroup.associate = (models) => {
    Reportgroup.hasMany(models.User, { foreignKey: 'reportgroup', as: 'users' });
  };
  return Reportgroup;
};