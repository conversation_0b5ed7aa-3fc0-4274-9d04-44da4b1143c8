const {Reportgroup} = require("../../../models")
const httpStatus = require("http-status");
const ApiError = require("../../../utils/ApiError");
const User = require("../../user/models/user.model");
// const activityService = require("../../activity/services/activity.service");
// const { _isObjectId } = require("../../../helpers/global.functions");
// const  mongoose = require("mongoose");

const addReportgroup = async(reqBody , user) =>{
    reqBody["user_id"] = user.id
 const reportgroup = await Reportgroup.create(reqBody)
 if(reqBody.createdBy){
    // await activityService.addActivity({actionOn: reqBody.createdBy, actionBy: reqBody.createdBy, actionName: "addReportgroup" , actionDescription: `reportgroup name ${reqBody.reportgroupDisplayName}` })
 }
 return reportgroup
}

const getReportgroups = async ({ filters, page, limit }, user) => {
    // console.log(user);

    // Clean and transform filters
    Object.entries(filters).forEach(([key, value]) => {
        if (value === "") delete filters[key];
    });

    // Define pagination options
    const offset = (page - 1) * limit;

    // Fetch reportgroups using Sequelize
    const reportgroupDocs = await Reportgroup.findAndCountAll({
        where: filters,
        attributes: ["reportgroupDisplayName", "reportgroup", "desc"],
        limit,
        offset,
    });

    return {
        total: reportgroupDocs.count,
        reportgroups: reportgroupDocs.rows,
        page,
        limit
    };
};

module.exports={
    addReportgroup,
    getReportgroups
}