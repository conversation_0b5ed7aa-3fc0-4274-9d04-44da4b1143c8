const express = require("express");
const router = express.Router();
const validate = require("../../../helpers/validate");
const  reportgroupValidation  = require("../validations/reportgroup.validations");
const reportgroupController = require("../controllers/reportgroup.controller");
const multer = require('multer');
const { storage } = require("../../../configuration/storage");
const { authorize } = require("../../../authwires/auth");
const upload = multer({ storage: storage });


/**
 * @swagger
 * /reportgroup/add-reportgroup:
 *   post:
 *     summary: add reportgroup
 *     tags:
 *       - Reportgroup
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               group_id:
 *                 type: number
 *                 description: Display name for the reportgroup
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/add-reportgroup', validate(reportgroupValidation.addReportgroup), reportgroupController.addReportgroup)



// authorize routes
// router.use(authorize)



/**
 * @swagger
 * /reportgroup/get-reportgroups:
 *   post:
 *     summary: fetch color preferences
 *     tags:
 *       - Reportgroup
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               filters:
 *                 type: object
 *                 description: reportgroup name
 *               page:
 *                type: number
 *               limit:
 *                type: number
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/get-reportgroups', validate(reportgroupValidation.getReportgroups), reportgroupController.getReportgroups)
// router.post('/get-reportgroup', validate(reportgroupValidation.getReportgroup), reportgroupController.getReportgroup)
// router.post('/delete-reportgroup', validate(reportgroupValidation.deleteReportgroup), reportgroupController.deleteReportgroup)


module.exports = router;
