const { catchAsync } = require("../../../utils/catchAsync");
// const { reportgroupService, tokenService } = require("../services");
const  reportgroupService  = require("../services/reportgroup.service")
const httpStatus = require("http-status");



const {handleRequest} = require("../../../helpers/handleRequest")
  module.exports.addReportgroup = handleRequest(reportgroupService.addReportgroup);
  module.exports.getReportgroups = handleRequest(reportgroupService.getReportgroups);
 