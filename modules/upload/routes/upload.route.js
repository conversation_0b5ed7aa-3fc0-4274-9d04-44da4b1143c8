const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const auth = require('../../auth/middlewares/auth');
const { catchAsync } = require('../../../utils/catchAsync');

// Configure multer storage
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(__dirname, '../../../uploads');
        const mediaDir = path.join(uploadDir, 'media');
        
        // Create directories if they don't exist
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir);
        }
        if (!fs.existsSync(mediaDir)) {
            fs.mkdirSync(mediaDir);
        }
        
        cb(null, mediaDir);
    },
    filename: function (req, file, cb) {
        // Generate unique filename
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, uniqueSuffix + path.extname(file.originalname));
    }
});

// Configure multer upload
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 100 * 1024 * 1024, // 100MB limit
    },
    fileFilter: function (req, file, cb) {
        // Accept video and image files
        const allowedTypes = ['video/mp4', 'video/quicktime', 'image/jpeg', 'image/png', 'image/gif'];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type. Only MP4, MOV, JPEG, PNG, and GIF files are allowed.'));
        }
    }
});

// Upload media file
router.post('/media', auth(), upload.single('file'), catchAsync(async (req, res) => {
    if (!req.file) {
        return res.status(400).json({
            success: false,
            message: 'No file uploaded'
        });
    }

    // Return the relative path to the uploaded file
    const relativePath = path.relative(
        path.join(__dirname, '../../../uploads'),
        req.file.path
    );

    res.status(200).json({
        success: true,
        message: 'File uploaded successfully',
        data: {
            media_url: relativePath,
            filename: req.file.filename,
            mimetype: req.file.mimetype,
            size: req.file.size
        }
    });
}));

module.exports = router; 