const express = require("express");
const router = express.Router();
const validate = require("../../../helpers/validate");
const  group_chatValidation  = require("../validations/group_chat.validations");
const group_chatController = require("../controllers/group_chat.controller");
const multer = require('multer');
const { storage } = require("../../../configuration/storage");
const { authorize } = require("../../../authwires/auth");
const upload = multer({ storage: storage });


router.use(authorize)

/**
 * @swagger
 * /group_chat/add-group_chat:
 *   post:
 *     summary: Add a new group chat message
 *     tags:
 *       - Group_chat
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Edit group chat message details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - group_chat_id
 *             properties:
 *               message:
 *                 type: string
 *                 description: ID of the chat message to edit
 *               reciever_id:
 *                 type: integer
 *                 description: Updated message content
 *               media_url:
 *                 type: array
 *                 items:
 *                  type: object
 *                  properties:
 *                    media_type:
 *                      type: string
 *                    url:
 *                      type: string
 *               is_group_chat:
 *                  type: boolean
 *                     
 *     responses:
 *        200:
 *          description: Message sent successfully
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/add-group_chat', validate(group_chatValidation.addGroup_chat), group_chatController.addGroup_chat)
/**
 * @swagger
 * /group_chat/edit-group_chat:
 *   post:
 *     summary: Edit a group chat message
 *     tags:
 *       - Group_chat
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Edit group chat message details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - group_chat_id
 *             properties:
 *               group_chat_id:
 *                 type: integer
 *                 description: ID of the chat message to edit
 *               message:
 *                 type: string
 *                 description: Updated message content
 *               is_deleted:
 *                 type: boolean
 *                 description: Flag to mark message as deleted
 *     responses:
 *        200:
 *          description: Message updated successfully
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/edit-group_chat', validate(group_chatValidation.editGroup_chat), group_chatController.editGroup_chat)



// authorize routes
// router.use(authorize)



/**
 * @swagger
 * /group_chat/get-group_chats:
 *   post:
 *     summary: Get group chat messages
 *     tags:
 *       - Group_chat
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Filter parameters for chat messages
 *           required: true
 *           schema:
 *             type: object
 *             properties:
 *               filters:
 *                 type: object
 *                 properties:
 *                   sender_id:
 *                     type: integer
 *                     description: Filter by sender ID
 *                   reciever_id:
 *                     type: integer
 *                     description: Filter by receiver ID
 *                   is_group_chat:
 *                     type: boolean
 *                     description: Filter group chat messages
 *                   is_deleted:
 *                     type: boolean
 *                     description: Include deleted messages
 *               page:
 *                type: integer
 *                description: Page number for pagination
 *               limit:
 *                type: integer
 *                description: Number of messages per page
 *     responses:
 *        200:
 *          description: Messages retrieved successfully
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/get-group_chats', validate(group_chatValidation.getGroup_chats), group_chatController.getGroup_chats)
// router.post('/get-group_chat', validate(group_chatValidation.getGroup_chat), group_chatController.getGroup_chat)
// router.post('/delete-group_chat', validate(group_chatValidation.deleteGroup_chat), group_chatController.deleteGroup_chat)


module.exports = router;
