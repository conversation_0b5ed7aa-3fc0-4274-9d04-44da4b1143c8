'use strict';

module.exports = (sequelize, DataTypes) => {
  const Group_chat = sequelize.define('Group_chat', {
    sender_id: {
      type: DataTypes.INTEGER
    },
    reciever_id: {
      type: DataTypes.INTEGER
    },
    message: {
      type: DataTypes.TEXT
    },
    media_url: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    is_group_chat: {
      type: DataTypes.BOOLEAN
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    created_at: {
      type: DataTypes.DATE
    },
    updated_at: {
      type: DataTypes.DATE
    },
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  });

  Group_chat.associate = (models) => {
    // Sender association
    Group_chat.belongsTo(models.User, {
      foreignKey: 'sender_id',
      as: 'sender'
    });

    // Receiver associations
    Group_chat.belongsTo(models.User, {
      foreignKey: 'reciever_id',
      as: 'receiver_user'
    });

    Group_chat.belongsTo(models.Group, {
      foreignKey: 'reciever_id',
      as: 'receiver_group'
    });
  };
  return Group_chat;
};