const Joi = require("joi");

module.exports.addGroup_chat = {
    body: Joi.object().keys({
        reciever_id: Joi.number().required(),
        message: Joi.string().required(),
        media_url: Joi.array().items(Joi.object()).optional().default([]),
        is_group_chat: Joi.boolean().optional().default(false)
    }),
};
module.exports.editGroup_chat = {
    body: Joi.object().keys({
        group_chat_id: Joi.number().required(),
        message: Joi.string().optional().allow(null, ""),
        is_deleted: Joi.boolean().optional().allow(null, "")
    }),
};
module.exports.getGroup_chats = {
    body: Joi.object().keys({
        filters: Joi.object().keys({
            sender_id: Joi.number().optional(),
            reciever_id: Joi.number().optional(),
            is_group_chat: Joi.boolean().optional(),
            is_deleted: Joi.boolean().optional()
        }).required(),
        page: Joi.number().required(),
        limit: Joi.number().required()
    }),
};