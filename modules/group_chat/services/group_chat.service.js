const {Group_chat, Follower, Group_chat_member, User, Group} = require("../../../models")
const httpStatus = require("http-status");
const ApiError = require("../../../utils/ApiError");
const { Sequelize , Op } = require('sequelize');
const { uploadToS3 } = require("../../aws/services/s3.service");
const { getIO } = require("../../../helpers/socket");
// const User = require("../../user/models/user.model");
// const activityService = require("../../activity/services/activity.service");
// const { _isObjectId } = require("../../../helpers/global.functions");
// const  mongoose = require("mongoose");

const addGroup_chat = async(reqBody,user) =>{
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }
    reqBody["sender_id"] = user["id"]
    const group_chat = await Group_chat.create(reqBody)
    if (reqBody.is_group_chat) {
        const roomName = `group_${reqBody.reciever_id}`; // group_id
        const io = getIO();
        io.to(roomName).emit("receive-message", group_chat);  // Group members will receive
      } else {
        // DM (Direct Message)
        const roomName = `user_${reqBody.reciever_id}`;
        const io = getIO();
        io.to(roomName).emit("receive-message", group_chat);  // Receiver will receive
      }
    if(reqBody.createdBy){
       // await activityService.addActivity({actionOn: reqBody.createdBy, actionBy: reqBody.createdBy, actionName: "addGroup_chat" , actionDescription: `group_chat name ${reqBody.group_chatDisplayName}` })
    }
    return group_chat
}
const editGroup_chat = async(reqBody) =>{
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }
    const [updatedCount, updatedGroup_chats] = await Group_chat.update(reqBody, {
        where: { id: reqBody.group_chat_id },
        returning: true,
    });

    if (updatedCount === 0) {
        throw new ApiError(httpStatus.NOT_FOUND, "Group_chat not found");
    }
    return updatedGroup_chats[0];
}

const getGroup_chats = async ({ filters, page, limit }, user) => {
    // Clean and transform filters
    Object.entries(filters).forEach(([key, value]) => {
        if (value === "") delete filters[key];
    });

    // Define pagination options
    const offset = (page - 1) * limit;

    // Fetch group_chats using Sequelize with associations
    const group_chatDocs = await Group_chat.findAndCountAll({
        where: filters,
        include: [
            {
                model: User,
                as: 'sender',
                attributes: ['id', 'username', 'profile_picture_url']
            },
            {
                model: User,
                as: 'receiver_user',
                attributes: ['id', 'username', 'profile_picture_url'],
                required: false,
                where: Sequelize.literal('"Group_chat"."is_group_chat" = false')
            },
            {
                model: Group,
                as: 'receiver_group',
                attributes: ['id', 'group_name'],
                required: false,
                where: Sequelize.literal('"Group_chat"."is_group_chat" = true')
            }
        ],
        limit,
        offset,
    });

    return {
        total: group_chatDocs.count,
        group_chats: group_chatDocs.rows,
        page,
        limit
    };
};

module.exports={
    addGroup_chat,
    getGroup_chats,
    editGroup_chat
}