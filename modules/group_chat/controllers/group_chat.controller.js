const { catchAsync } = require("../../../utils/catchAsync");
// const { group_chatService, tokenService } = require("../services");
// const { group_chatService } = require("../../../allservice")
const group_chatService = require("../services/group_chat.service")
const httpStatus = require("http-status");



const {handleRequest} = require("../../../helpers/handleRequest")


  module.exports.addGroup_chat = handleRequest(group_chatService.addGroup_chat);
  module.exports.editGroup_chat = handleRequest(group_chatService.editGroup_chat);
  module.exports.getGroup_chats = handleRequest(group_chatService.getGroup_chats);
 