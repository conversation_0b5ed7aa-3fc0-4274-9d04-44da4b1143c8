const {Hash, Follower} = require("../../../models")
const httpStatus = require("http-status");
const ApiError = require("../../../utils/ApiError");
const User = require("../../user/models/user.model");
// const activityService = require("../../activity/services/activity.service");
// const { _isObjectId } = require("../../../helpers/global.functions");
// const  mongoose = require("mongoose");
const { Op, Sequelize } = require("sequelize");

const addHash = async(reqBody) =>{
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }
 const hash = await Hash.create(reqBody)
 if(reqBody.createdBy){
    // await activityService.addActivity({actionOn: reqBody.createdBy, actionBy: reqBody.createdBy, actionName: "addHash" , actionDescription: `hash name ${reqBody.hashDisplayName}` })
 }
 return hash
}
const editHash = async(reqBody) =>{
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }
    const [updatedCount, updatedHashs] = await Hash.update(reqBody, {
        where: { id: reqBody.hash_id },
        returning: true,
    });

    if (updatedCount === 0) {
        throw new ApiError(httpStatus.NOT_FOUND, "Hash not found");
    }

    // await activityService.addActivity({
    //     actionOn: user?.id,
    //     actionBy: user?.id,
    //     actionName: "addDetails",
    //     actionDescription: "Adding personal details",
    // });

    return updatedHashs[0];
}

const getHashs = async ({ filters, page, limit }, user) => {
    // Clean and transform filters
    Object.entries(filters).forEach(([key, value]) => {
        if (value === "") delete filters[key];
    });

    // Add search functionality
    let searchCondition = {};
    if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        searchCondition = {
            [Op.or]: [
                Sequelize.literal(`LOWER("Hash"."hash_tag") LIKE '%${searchTerm}%'`)
            ]
        };
    }

    // Add search condition to existing filters
    if (filters.search) {
        delete filters.search;
        filters = {
            [Op.and]: [
                filters,
                searchCondition
            ]
        };
    }else{
        filters = {
            [Op.and]: [
                filters
            ]
        };
    }

    console.log(filters)

    // Define pagination options
    const offset = (page - 1) * limit;

    // Fetch hashs using Sequelize
    const hashDocs = await Hash.findAndCountAll({
        where: filters,
        limit,
        offset,
    });

    return {
        total: hashDocs.count,
        hashs: hashDocs.rows,
        page,
        limit
    };
};

module.exports={
    addHash,
    getHashs,
    editHash
}