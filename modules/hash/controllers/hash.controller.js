const { catchAsync } = require("../../../utils/catchAsync");
// const { hashService, tokenService } = require("../services");
// const { hashService } = require("../../../allservice")
const hashService = require("../services/hash.service")
const httpStatus = require("http-status");



const {handleRequest} = require("../../../helpers/handleRequest")
  module.exports.addHash = handleRequest(hashService.addHash);
  module.exports.editHash = handleRequest(hashService.editHash);
  module.exports.getHashs = handleRequest(hashService.getHashs);
 