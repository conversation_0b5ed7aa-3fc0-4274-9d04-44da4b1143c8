'use strict';

module.exports = (sequelize, DataTypes) => {
  const Hash = sequelize.define('Hash', {
    hash_tag:{
      type: DataTypes.STRING,
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    created_at:{
      type: DataTypes.DATE
    },
    updated_at:{
      type: DataTypes.DATE
    },
  }, {
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });

    // Hash.associate = (models) => {
    //   Hash.belongsTo(models.User, { foreignKey: 'hashged_user_id', as: 'hashgedUser' });
    //   Hash.belongsTo(models.Post, { foreignKey: 'post_id', as: 'post' });
    // };
    
  return Hash;
};