const Joi = require("joi");

module.exports.addHash = {
    body: Joi.object().keys({
      hash_tag: Joi.string().required()
    }),
};
module.exports.editHash = {
    body: Joi.object().keys({
        hash_id: Joi.number().required(),
        hash_tag: Joi.string().optional().allow(null, ""),
        is_deleted: Joi.boolean().optional().allow(null, "")
    }),
};
module.exports.getHashs = {
    body: Joi.object().keys({
        filters: Joi.object().required(),
        page: Joi.number().required(),
        limit: Joi.number().required()
    }),
};