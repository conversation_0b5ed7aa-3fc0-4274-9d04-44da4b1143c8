const express = require("express");
const router = express.Router();
const validate = require("../../../helpers/validate");
const  hashValidation  = require("../validations/hash.validations");
const hashController = require("../controllers/hash.controller");
const multer = require('multer');
const { storage } = require("../../../configuration/storage");
const { authorize } = require("../../../authwires/auth");
const upload = multer({ storage: storage });


router.use(authorize)

/**
 * @swagger
 * /hash/add-hash:
 *   post:
 *     summary: add hash
 *     tags:
 *       - Hash
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               hash_tag:
 *                 type: string
 *                 description: Display name for the hash
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/add-hash', validate(hashValidation.addHash), hashController.addHash)
/**
 * @swagger
 * /hash/edit-hash:
 *   post:
 *     summary: edit hash
 *     tags:
 *       - Hash
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               hash_id:
 *                 type: number
 *               hash_tag:
 *                 type: string
 *                 description: Display name for the hash
 *               is_deleted:
 *                 type: boolean
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/edit-hash', validate(hashValidation.editHash), hashController.editHash)



// authorize routes
// router.use(authorize)



/**
 * @swagger
 * /hash/get-hashs:
 *   post:
 *     summary: fetch hashs
 *     tags:
 *       - Hash
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               filters:
 *                 type: object
 *                 description: hash name
 *               page:
 *                type: number
 *               limit:
 *                type: number
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/get-hashs', validate(hashValidation.getHashs), hashController.getHashs)
// router.post('/get-hash', validate(hashValidation.getHash), hashController.getHash)
// router.post('/delete-hash', validate(hashValidation.deleteHash), hashController.deleteHash)


module.exports = router;
