const {Like, Follower, User, Post} = require("../../../models")
const httpStatus = require("http-status");
const ApiError = require("../../../utils/ApiError");
const { Sequelize } = require("sequelize");
const { client: redisClient, set: redisSet, get: redisGet, setHash: redisSetHash, getHash: redisGetHash, getAllHash: redisGetAllHash } = require("../../../services/redis.service");
const logger = require("../../../utils/logger");
const Friend = require("../../friend/models/friend.model");
const { Op } = require('sequelize');
// const User = require("../../user/models/user.model");
// const activityService = require("../../activity/services/activity.service");
// const { _isObjectId } = require("../../../helpers/global.functions");
// const  mongoose = require("mongoose");

// Redis key generators
const getPostKey = (postId) => `post:${postId}`;
const getPostsListKey = (userId) => `posts:${userId}`;

// Helper function to update post cache with new like count
const updatePostCache = async (postId, userId, isLiked) => {
    try {
        // Update individual post cache
        const postKey = getPostKey(postId);
        const cachedPost = await redisGet(postKey);
        
        if (cachedPost) {
            cachedPost.likeCount = isLiked ? 
                (parseInt(cachedPost.likeCount) + 1) : 
                (parseInt(cachedPost.likeCount) - 1);
            cachedPost.isLiked = isLiked;
            await redisSet(postKey, cachedPost);
            logger.info(`Updated post cache for post ${postId} with new like count: ${cachedPost.likeCount}`);
        }

        // Update post in all users' lists
        const allKeys = await redisClient.keys('posts:*');
        for (const key of allKeys) {
            if (key.includes(':count:')) continue; // Skip count keys
            
            const existingPosts = await redisGetAllHash(key) || {};
            if (existingPosts[postId]) {
                const post = existingPosts[postId];
                post.likeCount = isLiked ? 
                    (parseInt(post.likeCount) + 1) : 
                    (parseInt(post.likeCount) - 1);
                post.isLiked = isLiked;
                await redisSetHash(key, postId, post);
                logger.info(`Updated post in list ${key} with new like count: ${post.likeCount}`);
            }
        }
    } catch (error) {
        logger.error(`Error updating post cache for post ${postId}:`, error);
    }
};

const addLike = async(reqBody, user) =>{
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }
    reqBody["user_id"] = user.id

    let like = reqBody.comment_id?
    await Like.findOne({where: {post_id: reqBody.post_id, user_id: user.id , is_deleted: false , comment_id: reqBody.comment_id}}) :
    await Like.findOne({where: {post_id: reqBody.post_id, user_id: user.id , is_deleted: false , comment_id: null}}) 

    if(like) {
        await like.update({is_deleted: true})
        // Update cache - like removed
        await updatePostCache(reqBody.post_id, user.id, false);
    }else{
        like = await Like.create(reqBody)
        // Update cache - like added
        await updatePostCache(reqBody.post_id, user.id, true);
    }
    if(reqBody.createdBy){
       // await activityService.addActivity({actionOn: reqBody.createdBy, actionBy: reqBody.createdBy, actionName: "addLike" , actionDescription: `like name ${reqBody.likeDisplayName}` })
    }
    return like
}
const editLike = async(reqBody, user) =>{
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }
    let query = {}
    if(reqBody.post_id){
        query = {
            post_id: reqBody.post_id,
            user_id: user.id,
            is_deleted: false
        }
    }else{
        query = {id:reqBody.like_id}
    }
    const [updatedCount, updatedLikes] = await Like.update(reqBody, {
        where: query,
        returning: true,
    });

    if (updatedCount === 0) {
        throw new ApiError(httpStatus.NOT_FOUND, "Like not found");
    }

    // Update cache if post_id is provided
    if (reqBody.post_id) {
        const isLiked = !reqBody.is_deleted;
        await updatePostCache(reqBody.post_id, user.id, isLiked);
    }

    return updatedLikes[0];
}

const getLikes = async ({ filters, page, limit }, user) => {
    // Clean and transform filters
    Object.entries(filters).forEach(([key, value]) => {
        if (value === "") delete filters[key];
    });

    // Define pagination options
    const offset = (page - 1) * limit;

    filters = {
        ...filters,
        is_deleted: false
    }

    if(!filters.comment_id) filters = {
        ...filters,
        comment_id: null
    }

    // Fetch likes using Sequelize
    const likeDocs = await Like.findAndCountAll({
        include: [
            {
                model: User,
                as: 'user',
                attributes: [
                    "id", 
                    "username", 
                    "email", 
                    "profile_picture_url",
                    "first_name",
                    "last_name",
                    "date_of_birth",
                    "mobile_number"
                ]
            }
        ],
        where: filters,
        attributes: ["post_id", "user_id", "created_at"],
        limit,
        offset,
    });

    // Get friend statuses and follower counts for all users using Neo4j
    const likes = await Promise.all(likeDocs.rows.map(async (like) => {
        const userData = like.user.toJSON();
        
        // Get friend status using Neo4j
        const myStatus = await Friend.getFriendshipStatus(user.id, userData.id);
        const friendStatus = await Friend.getFriendshipStatus(userData.id, user.id);
        
        // Get follower count using Neo4j
        const followers = await Friend.getFollowers(userData.id);
        const followerCount = followers.length;

        return {
            ...like.toJSON(),
            user: {
                ...userData,
                myStatus: myStatus?.status || null,
                friendStatus: friendStatus?.status || null,
                followerCount
            }
        };
    }));

    return {
        total: likeDocs.count,
        likes,
        page,
        limit
    };
};

module.exports={
    addLike,
    getLikes,
    editLike
}