const Joi = require("joi");

module.exports.addLike = {
    body: Joi.object().keys({
        post_id: Joi.number().required(),
        comment_id: Joi.number().optional().allow(null, ""),
        user_id: Joi.number().optional().allow(null , "")
    }),
};
module.exports.editLike = {
    body: Joi.object().keys({
        like_id: Joi.number().optional().allow(null, ""),
        post_id: Joi.number().optional().allow(null, ""),
        user_id: Joi.number().optional().allow(null , ""),
        is_deleted: Joi.boolean().optional().allow(null, "")
    }),
};
module.exports.getLikes = {
    body: Joi.object().keys({
        filters: Joi.object().required(),
        page: Joi.number().required(),
        limit: Joi.number().required()
    }),
};