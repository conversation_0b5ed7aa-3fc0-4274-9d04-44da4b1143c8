const { catchAsync } = require("../../../utils/catchAsync");
// const { likeService, tokenService } = require("../services");
// const { likeService } = require("../../../allservice")
const likeService = require("../services/like.service")
const httpStatus = require("http-status");



const {handleRequest} = require("../../../helpers/handleRequest")
  module.exports.addLike = handleRequest(likeService.addLike);
  module.exports.editLike = handleRequest(likeService.editLike);
  module.exports.getLikes = handleRequest(likeService.getLikes);
 