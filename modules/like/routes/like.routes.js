const express = require("express");
const router = express.Router();
const validate = require("../../../helpers/validate");
const  likeValidation  = require("../validations/like.validations");
const likeController = require("../controllers/like.controller");
const multer = require('multer');
const { storage } = require("../../../configuration/storage");
const { authorize } = require("../../../authwires/auth");
const upload = multer({ storage: storage });


router.use(authorize)

/**
 * @swagger
 * /like/add-like:
 *   post:
 *     summary: add like
 *     tags:
 *       - Like
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               post_id:
 *                 type: number
 *                 description: Display name for the like
 *               comment_id:
 *                 type: number
 *               user_id:
 *                 type: number
 *                 description: Like of the user
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/add-like', validate(likeValidation.addLike), likeController.addLike)
/**
 * @swagger
 * /like/edit-like:
 *   post:
 *     summary: edit like
 *     tags:
 *       - Like
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               like_id:
 *                 type: number
 *               user_id:
 *                 type: number
 *                 description: Display name for the like
 *               post_id:
 *                 type: number
 *                 description: Like of the user
 *               is_deleted:
 *                 type: boolean
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/edit-like', validate(likeValidation.editLike), likeController.editLike)



// authorize routes
// router.use(authorize)



/**
 * @swagger
 * /like/get-likes:
 *   post:
 *     summary: fetch color preferences
 *     tags:
 *       - Like
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               filters:
 *                 type: object
 *                 description: like name
 *               page:
 *                type: number
 *               limit:
 *                type: number
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/get-likes', validate(likeValidation.getLikes), likeController.getLikes)
// router.post('/get-like', validate(likeValidation.getLike), likeController.getLike)
// router.post('/delete-like', validate(likeValidation.deleteLike), likeController.deleteLike)


module.exports = router;
