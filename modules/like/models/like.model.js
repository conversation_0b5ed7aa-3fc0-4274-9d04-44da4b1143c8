'use strict';

module.exports = (sequelize, DataTypes) => {
  const Like = sequelize.define('Like', {
    post_id: {
      type: DataTypes.INTEGER
    },
    comment_id: {
      type: DataTypes.INTEGER,
      defaultValue: null
    },
    user_id:{
      type: DataTypes.INTEGER
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    created_at:{
      type: DataTypes.DATE
    },
    updated_at:{
      type: DataTypes.DATE
    },
  }, {
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });

    Like.associate = (models) => {
      Like.belongsTo(models.Post, { foreignKey: 'post_id', as: 'post' });
      Like.belongsTo(models.User, { foreignKey: 'user_id', as: 'user' });
    };
  return Like;
};