'use strict';

module.exports = (sequelize, DataTypes) => {
  const Post = sequelize.define('Post', {
    user_id: {
      type: DataTypes.INTEGER
    },
    group_id: {
      type: DataTypes.INTEGER
    },
    content: {
      type: DataTypes.TEXT
    },
    media_url: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    hash_tags: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    location: {
      type: DataTypes.STRING,
      allowNull: true
    },
    latitude:{
      type: DataTypes.STRING,
      allowNull: true
    },
    is_donation:{
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    longitude:{
      type: DataTypes.STRING,
      allowNull: true
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    created_at:{
      type: DataTypes.DATE
    },
    updated_at:{
      type: DataTypes.DATE
    },
    start_date:{
      type: DataTypes.DATE
    },
    end_date:{
      type: DataTypes.DATE
    },
    category:{
      type: DataTypes.STRING
    },
    donation_target:{
      type: DataTypes.STRING
    }
  }, {
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });

    Post.associate = (models) => {
      Post.belongsTo(models.User, { foreignKey: 'user_id', as: 'user' });
      Post.belongsTo(models.Group, { foreignKey: 'group_id', as: 'group' });
      Post.hasMany(models.Like, { foreignKey: 'post_id', as: 'likes' });
      Post.hasMany(models.Comment, { foreignKey: 'post_id', as: 'comments' });
      Post.hasMany(models.Tag, { foreignKey: 'post_id', as: 'tags' });
      Post.hasMany(models.Donator, { foreignKey: 'post_id', as: 'donators' });
    };
  return Post;
};