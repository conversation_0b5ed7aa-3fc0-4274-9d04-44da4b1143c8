const { catchAsync } = require("../../../utils/catchAsync");
// const { postService, tokenService } = require("../services");
// const { postService } = require("../../../allservice")
const postService = require("../services/post.service")
const httpStatus = require("http-status");



const {handleRequest} = require("../../../helpers/handleRequest")

  

  module.exports.addPost = handleRequest(postService.addPost);
  module.exports.editPost = handleRequest(postService.editPost);
  module.exports.getPosts = handleRequest(postService.getPosts);
  module.exports.getPost = handleRequest(postService.getPost, true);
 