const {Post, Tag, User, Group, Group_member , Hash, Like, Donator} = require("../../../models")
const Friend = require("../../friend/models/friend.model");
const notificationService = require("../../notification/services/notification.service");
const httpStatus = require("http-status");
const ApiError = require("../../../utils/ApiError");
const { Sequelize , Op } = require('sequelize');
const { uploadToS3 } = require("../../aws/services/s3.service");
const { client: redisClient, set: redisSet, get: redisGet, setHash: redisSetHash, getHash: redisGetHash, getAllHash: redisGetAllHash } = require("../../../services/redis.service");
const logger = require("../../../utils/logger");
// const activityService = require("../../activity/services/activity.service");
// const { _isObjectId } = require("../../../helpers/global.functions");
// const  mongoose = require("mongoose");

// Redis key generators
const getPostKey = (postId) => `post:${postId}`;
const getPostsListKey = (userId) => `posts:${userId}`;
const getPostsCountKey = (userId) => `posts:count:${userId}`;

const addPost = async(reqBody, user) => {
    try {
        console.log("Adding new post for user:", user.id);
        
        // Validate user
        if (!user || !user.id) {
            throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid user information');
        }

        // Clean up empty values
        for(let [key, value] of Object.entries(reqBody)){
            if(value === "") delete reqBody[key];
        }

        // Handle hash tags
        if(reqBody.hash_tags && reqBody.hash_tags.length > 0){
            for(let hash of reqBody.hash_tags){
                const hashTag = await Hash.findOne({where: {hash_tag: hash}});
                if(!hashTag){
                    await Hash.create({hash_tag: hash});
                }
            }
        }
       
        // Add user ID to the post
        reqBody.user_id = user.id;

        // Create the post
        const post = await Post.create(reqBody);
        console.log("Created new post with ID:", post.id);

        // Handle tagged users
        if(reqBody.tagged_user_ids && reqBody.tagged_user_ids.length > 0){
            for(let tag_id of reqBody.tagged_user_ids){
                await Tag.create({post_id: post.id, tagged_user_id: tag_id});
            }
        }

        // Get the complete post with all associations and computed fields
        const completePost = await Post.findOne({
            where: { id: post.id },
            attributes: [
                "id",
                "user_id",
                "content",
                "media_url",
                "location",
                "created_at",
                "start_date",
                "end_date",
                "category",
                "donation_target",
                "is_donation",
                [Sequelize.literal('(CASE WHEN "Post"."user_id" = ' + user.id + ' THEN true ELSE false END)'), 'isOwn'],
                [Sequelize.literal(`(
                    SELECT COUNT(*) 
                    FROM "Likes" AS "l" 
                    WHERE 
                        "l"."post_id" = "Post"."id" AND 
                        "l"."is_deleted" = false AND
                        "l"."comment_id" IS NULL
                )`), 'likeCount'],
                [Sequelize.literal(`(
                    SELECT COUNT(*) 
                    FROM "Comments" AS "c" 
                    WHERE 
                        "c"."post_id" = "Post"."id" AND 
                        "c"."is_deleted" = false
                )`), 'commentCount'],
                [Sequelize.literal(`(
                    SELECT COUNT(*) > 0 
                    FROM "Likes" AS "l" 
                    WHERE 
                        "l"."post_id" = "Post"."id" AND 
                        "l"."user_id" = ${user.id} AND
                        "l"."is_deleted" = false
                )`), 'isLiked'],
                [Sequelize.literal(`(
                    SELECT COUNT(*) 
                    FROM "Donators" AS "d" 
                    WHERE 
                        "d"."post_id" = "Post"."id" AND 
                        "d"."is_deleted" = false
                )`), 'donatorCount'],
                [Sequelize.literal(`(
                    SELECT COUNT(*) > 0 
                    FROM "Donators" AS "d" 
                    WHERE 
                        "d"."post_id" = "Post"."id" AND 
                        "d"."user_id" = ${user.id} AND
                        "d"."is_deleted" = false
                )`), 'isDonatord'],
                [Sequelize.literal(`(
                    SELECT COALESCE(SUM(CAST("d"."donatedAmount" AS DECIMAL)), 0)
                    FROM "Donators" AS "d"
                    WHERE 
                        "d"."post_id" = "Post"."id" AND 
                        "d"."is_deleted" = false
                )`), 'totalDonatedAmount']
            ],
            include: [
                {
                    model: User,
                    as: 'user',
                    attributes: ["id", "username", "email", "profile_picture_url", "privacy_status", "first_name", "last_name"]
                },
                {
                    model: Tag,
                    as: 'tags',
                    where: { is_deleted: false },
                    required: false,
                    include: [{
                        model: User,
                        as: 'taggedUser',
                        attributes: ['id', 'username', 'first_name', 'last_name']
                    }]
                },
                {
                    model: Group,
                    as: 'group',
                    attributes: ["id", "group_name"]
                },
                {
                    model: Donator,
                    as: 'donators',
                    where: { is_deleted: false },
                    required: false,
                    include: [{
                        model: User,
                        as: 'user',
                        attributes: ['id', 'username', 'first_name', 'last_name', 'profile_picture_url']
                    }]
                }
            ],
        });

        if (!completePost) {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to retrieve created post');
        }

        // Get friend status using Neo4j
        const myStatus = await Friend.getFriendshipStatus(user.id, completePost.user_id);
        const friendStatus = await Friend.getFriendshipStatus(completePost.user_id, user.id);
        const isFriend = await Friend.getFriendshipStatus(user.id, completePost.user_id)?.status === 'accepted';

        // Add friend status to post
        const postWithFriendStatus = {
            ...completePost.toJSON(),
            myStatus: myStatus?.status || null,
            friendStatus: friendStatus?.status || null,
            isFriend
        };

        try {
            // Cache the new post
            const postKey = getPostKey(post.id);
            await redisSet(postKey, postWithFriendStatus);
            console.log("Cached individual post with key:", postKey);

            // Update posts list in Redis
            const postsListKey = getPostsListKey(user.id);
            await redisSetHash(postsListKey, post.id, postWithFriendStatus);
            console.log("Added post to user's posts list with key:", postsListKey);

            // Update count
            const countKey = getPostsCountKey(user.id);
            const currentCount = await redisGet(countKey) || 0;
            const newCount = parseInt(currentCount) + 1;
            await redisSet(countKey, newCount);
            console.log("Updated post count to:", newCount);
        } catch (error) {
            console.error('Error caching post:', error);
            // Continue even if caching fails
        }

        return postWithFriendStatus;
    } catch (error) {
        console.error('Error in addPost:', error);
        if (error instanceof ApiError) {
            throw error;
        }
        throw new ApiError(
            httpStatus.INTERNAL_SERVER_ERROR,
            `Error creating post: ${error.message || 'Unknown error'}`
        );
    }
};

const editPost = async(reqBody , user) =>{
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }
    // reqBody.user_id = reqBody.user_id
    if(reqBody.hash_tags && reqBody.hash_tags.length > 0){
        for(let hash of reqBody.hash_tags){
            const hashTag = await Hash.findOne({where: {hash_tag: hash}})
            if(!hashTag){
                await Hash.create({hash_tag: hash})
            }
        }
    }
    let updatedPosts = await Post.findOne({where: {id: reqBody.post_id}})
    let updatedCount = updatedPosts? 1 : 0

    // const [updatedCount, updatedPosts] = await Post.update(reqBody, {
    //     where: { id: reqBody.post_id },
    //     returning: true,
    // });

    if (updatedCount === 0) {
        throw new ApiError(httpStatus.NOT_FOUND, "Post not found");
    }
    await updatedPosts.update(reqBody)

    // Get the complete updated post with all associations and computed fields
    const completePost = await Post.findOne({
        where: { id: reqBody.post_id },
        attributes: [
            "id",
            "user_id",
            "content",
            "media_url",
            "location",
            "created_at",
            "start_date",
            "end_date",
            "category",
            "donation_target",
            "is_donation",
            [Sequelize.literal('(CASE WHEN "Post"."user_id" = :userId THEN true ELSE false END)'), 'isOwn'],
            [Sequelize.literal(`(
                SELECT COUNT(*) 
                FROM "Likes" AS "l" 
                WHERE 
                    "l"."post_id" = "Post"."id" AND 
                    "l"."is_deleted" = false AND
                    "l"."comment_id" IS NULL
            )`), 'likeCount'],
            [Sequelize.literal(`(
                SELECT COUNT(*) 
                FROM "Comments" AS "c" 
                WHERE 
                    "c"."post_id" = "Post"."id" AND 
                    "c"."is_deleted" = false
            )`), 'commentCount'],
            [Sequelize.literal(`(
                SELECT COUNT(*) > 0 
                FROM "Likes" AS "l" 
                WHERE 
                    "l"."post_id" = "Post"."id" AND 
                    "l"."user_id" = :userId AND
                    "l"."is_deleted" = false
            )`), 'isLiked'],
            [Sequelize.literal(`(
                SELECT COUNT(*) 
                FROM "Donators" AS "d" 
                WHERE 
                    "d"."post_id" = "Post"."id" AND 
                    "d"."is_deleted" = false
            )`), 'donatorCount'],
            [Sequelize.literal(`(
                SELECT COUNT(*) > 0 
                FROM "Donators" AS "d" 
                WHERE 
                    "d"."post_id" = "Post"."id" AND 
                    "d"."user_id" = :userId AND
                    "d"."is_deleted" = false
            )`), 'isDonatord'],
            [Sequelize.literal(`(
                SELECT COALESCE(SUM(CAST("d"."donatedAmount" AS DECIMAL)), 0)
                FROM "Donators" AS "d"
                WHERE 
                    "d"."post_id" = "Post"."id" AND 
                    "d"."is_deleted" = false
            )`), 'totalDonatedAmount']
        ],
        include: [
            {
                model: User,
                as: 'user',
                attributes: ["id", "username", "email", "profile_picture_url", "privacy_status", "first_name", "last_name"]
            },
            {
                model: Tag,
                as: 'tags',
                where: { is_deleted: false },
                required: false,
                include: [{
                    model: User,
                    as: 'taggedUser',
                    attributes: ['id', 'username', 'first_name', 'last_name']
                }]
            },
            {
                model: Group,
                as: 'group',
                attributes: ["id", "group_name"]
            },
            {
                model: Donator,
                as: 'donators',
                where: { is_deleted: false },
                required: false,
                include: [{
                    model: User,
                    as: 'user',
                    attributes: ['id', 'username', 'first_name', 'last_name', 'profile_picture_url']
                }]
            }
        ],
    });

    // Get friend status using Neo4j
    const myStatus = await Friend.getFriendshipStatus(user.id, updatedPosts.user_id);
    const friendStatus = await Friend.getFriendshipStatus(updatedPosts.user_id, user.id);
    const isFriend = await Friend.getFriendshipStatus(user.id, updatedPosts.user_id)?.status === 'accepted';

    // Add friend status to post
    const postWithFriendStatus = {
        ...updatedPosts.toJSON(),
        myStatus: myStatus?.status || null,
        friendStatus: friendStatus?.status || null,
        isFriend
    };

    // Update post in cache
    await redisSet(getPostKey(reqBody.post_id), postWithFriendStatus);

    // Update post in all users' lists
    const allKeys = await redisClient.keys('posts:*');
    for (const key of allKeys) {
        if (key.includes(':count:')) continue; // Skip count keys
        
        const existingPosts = await redisGetAllHash(key) || {};
        if (existingPosts[reqBody.post_id]) {
            await redisSetHash(key, reqBody.post_id, postWithFriendStatus);
        }
    }

    // await activityService.addActivity({
    //     actionOn: user?.id,
    //     actionBy: user?.id,
    //     actionName: "addDetails",
    //     actionDescription: "Adding personal details",
    // });

    return postWithFriendStatus;
}

const getPosts = async ({ filters, page, limit }, user) => {
    try {
        console.log("Getting posts for user:", user.id, "with filters:", filters);
        
        // Validate user
        if (!user || !user.id) {
            throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid user information');
        }

        // Convert page and limit to integers with validation
        page = parseInt(page, 10);
        limit = parseInt(limit, 10);
        
        if (isNaN(page) || page < 1) {
            page = 1;
        }
        if (isNaN(limit) || limit < 1) {
            limit = 10;
        }
        
        // Handle direct field filters
        const directFilters = {};
        const specialFilters = {};
        
        if (filters) {
            Object.entries(filters).forEach(([key, value]) => {
                if (value === "") {
                    delete filters[key];
                } else if (['id', 'is_deleted', 'user_id', 'group_id', 'created_at', 'updated_at'].includes(key)) {
                    directFilters[key] = value;
                } else {
                    specialFilters[key] = value;
                }
            });
        }

        // Get friend IDs first
        let friendIds = [];
        try {
            friendIds = await getFriendIds(user.id);
            console.log("Friend IDs:", friendIds);
        } catch (error) {
            console.error('Error getting friend IDs:', error);
            // Continue with empty friend list
        }

        // Try to get from cache first
        const postsListKey = getPostsListKey(user.id);
        const countKey = getPostsCountKey(user.id);
        
        let cachedPosts = null;
        let cachedCount = null;
        
        try {
            cachedPosts = await redisGetAllHash(postsListKey);
            cachedCount = await redisGet(countKey);
            console.log("Cache status - Posts:", cachedPosts ? Object.keys(cachedPosts).length : 0, "Count:", cachedCount);
        } catch (error) {
            console.error('Error reading from cache:', error);
            // Continue with database query if cache read fails
        }

        if (cachedPosts && cachedCount) {
            console.log("Using cached posts");
            // Convert hash to array and apply filters
            let posts = Object.values(cachedPosts);
            console.log("Total cached posts before filtering:", posts.length);

            // Update isLiked status for each post based on current user
            posts = await Promise.all(posts.map(async (post) => {
                const isLiked = await Like.findOne({
                    where: {
                        post_id: post.id,
                        user_id: user.id,
                        is_deleted: false,
                        comment_id: null
                    }
                });
                return {
                    ...post,
                    isLiked: !!isLiked
                };
            }));
            
            // Apply direct field filters first
            if (Object.keys(directFilters).length > 0) {
                posts = posts.filter(post => {
                    return Object.entries(directFilters).every(([key, value]) => {
                        if (key === 'created_at' || key === 'updated_at') {
                            // Handle date comparisons if needed
                            return true; // Add date comparison logic if required
                        }
                        return post[key] === value;
                    });
                });
            }
            
            // Apply special filters
            if (specialFilters.search) {
                const searchTerm = specialFilters.search.toLowerCase();
                posts = posts.filter(post => 
                    post.content.toLowerCase().includes(searchTerm) ||
                    (post.user && (
                        post.user.username.toLowerCase().includes(searchTerm) ||
                        post.user.first_name.toLowerCase().includes(searchTerm) ||
                        post.user.last_name.toLowerCase().includes(searchTerm)
                    ))
                );
            }

            if (specialFilters.postType === "friend") {
                console.log("Filtering friend posts from cache");
                posts = posts.filter(post => friendIds.includes(post.user_id));
                console.log("Posts after friend filter:", posts.length);
            } else if (specialFilters.postType === "mypost") {
                posts = posts.filter(post => post.user_id === user.id);
            } else if (specialFilters.postType === "group") {
                const groupIds = await getGroupIds(user.id);
                posts = posts.filter(post => post.group_id && groupIds.includes(post.group_id));
            }

            // Apply privacy filters
            const users = await User.findAll({
                attributes: ['id', 'privacy_status']
            });
            const userPrivacyMap = new Map(users.map(u => [u.id, u.privacy_status]));

            posts = posts.filter(post => {
                // Always show user's own posts
                if (post.user_id === user.id) return true;
                
                const postUserPrivacy = userPrivacyMap.get(post.user_id);
                
                // Show public posts
                if (postUserPrivacy === 'public') return true;
                
                // Show posts from friends
                if (friendIds.includes(post.user_id)) return true;
                
                return false;
            });

            // Get blocked users
            const blockedUsers = await getBlockedUsers(user.id);
            const blockedUserIds = blockedUsers.map(friend => friend.id);

            // Filter out blocked users' posts
            if (blockedUserIds.length > 0) {
                posts = posts.filter(post => !blockedUserIds.includes(post.user_id));
            }

            console.log("Posts after filtering:", posts.length);

            // Apply pagination
            const start = (page - 1) * limit;
            const end = start + limit;
            const paginatedPosts = posts.slice(start, end);
            console.log("Final paginated posts:", paginatedPosts.length);

            return {
                total: posts.length,
                posts: paginatedPosts,
                page,
                limit
            };
        }

        console.log("Fetching posts from database");
        // If not in cache, proceed with database query
        let searchCondition = {};
        if (specialFilters.search) {
            const searchTerm = specialFilters.search.toLowerCase();
            let whereClause = {
                is_deleted: false,
                [Op.or]: [
                    Sequelize.literal(`LOWER("User"."username") LIKE '%${searchTerm}%'`),
                    Sequelize.literal(`LOWER("User"."first_name") LIKE '%${searchTerm}%'`),
                    Sequelize.literal(`LOWER("User"."last_name") LIKE '%${searchTerm}%'`),
                    Sequelize.literal(`LOWER(CONCAT("User"."first_name", ' ', "User"."last_name")) LIKE '%${searchTerm}%'`)
                ]
            };
            let userIds = await User.findAll({where: whereClause });
            userIds = userIds.map((user) => user.id);
            
            searchCondition = {
                [Op.or]: [
                    { user_id: { [Op.in]: userIds } },
                    Sequelize.literal(`LOWER("Post"."content") LIKE '%${searchTerm}%'`)
                ]
            };
        }

        // Get blocked users
        const blockedUsers = await getBlockedUsers(user.id);
        const blockedUserIds = blockedUsers.map(friend => friend.id);

        // Get all users with their privacy settings
        const users = await User.findAll({
            attributes: ['id', 'privacy_status']
        });

        // Create a map of user IDs to their privacy settings
        const userPrivacyMap = new Map(users.map(u => [u.id, u.privacy_status]));

        // Modified privacy filter to be more inclusive
        let privacyFilter = {
            [Op.or]: [
                // User's own posts
                { user_id: user.id },
                // Posts from users with public privacy
                {
                    user_id: {
                        [Op.in]: users
                            .filter(u => u.privacy_status === 'public')
                            .map(u => u.id)
                    }
                },
                // Posts from friends
                {
                    user_id: {
                        [Op.in]: friendIds
                    }
                }
            ]
        };

        if (blockedUserIds.length > 0) {
            filters = {
                ...filters,
                [Op.and]: [
                    {
                        [Op.not]: {
                            user_id: { [Op.in]: blockedUserIds }
                        }
                    }
                ]
            };
        }

        if (specialFilters.postType === "group") {
            const groups = await Group_member.findAll({ where: { user_id: user.id } });
            const myGroupIds = groups.map((g) => g.group_id);
            const members = await Group_member.findAll({ where: { group_id: { [Op.in]: myGroupIds } } });
            const allMembers = [...new Set(members.map((m) => m.user_id))];
            let query = {
                user_id: { [Op.in]: allMembers }
            };
            filters = {
                ...filters,
                ...query
            };
            delete specialFilters.postType;
        } else if (specialFilters.postType === "friend") {
            console.log("Applying friend filter to database query");
            if (friendIds.length > 0) {
                let query = {
                    user_id: { [Op.in]: friendIds }
                };
                filters = { ...filters, ...query };
                console.log("Friend filter applied:", query);
            } else {
                console.log("No friends found, returning empty result");
                return {
                    total: 0,
                    posts: [],
                    page,
                    limit
                };
            }
            delete specialFilters.postType;
        } else if (specialFilters.postType === "mypost") {
            delete specialFilters.postType;
            let query = {
                user_id: user.id
            };
            filters = { ...filters, ...query };
        }

        let search = specialFilters.search;
        delete specialFilters.search;

        // Combine all filters
        const finalFilters = {
            [Op.and]: [
                { is_deleted: false }, // Add base filter for non-deleted posts
                directFilters,
                privacyFilter,
                (search ? searchCondition : {})
            ]
        };

        const offset = (page - 1) * limit;

        console.log("Final filters:", JSON.stringify(finalFilters, null, 2));

        const postDocs = await Post.findAndCountAll({
            where: finalFilters,
            attributes: [
                "id",
                "user_id",
                "content",
                "media_url",
                "location",
                "created_at",
                "start_date",
                "end_date",
                "category",
                "donation_target",
                "is_donation",
                [Sequelize.literal('(CASE WHEN "Post"."user_id" = ' + user.id + ' THEN true ELSE false END)'), 'isOwn'],
                [Sequelize.literal(`(
                    SELECT COUNT(*) 
                    FROM "Likes" AS "l" 
                    WHERE 
                        "l"."post_id" = "Post"."id" AND 
                        "l"."is_deleted" = false AND
                        "l"."comment_id" IS NULL
                )`), 'likeCount'],
                [Sequelize.literal(`(
                    SELECT COUNT(*) 
                    FROM "Comments" AS "c" 
                    WHERE 
                        "c"."post_id" = "Post"."id" AND 
                        "c"."is_deleted" = false
                )`), 'commentCount'],
                [Sequelize.literal(`(
                    SELECT COUNT(*) > 0 
                    FROM "Likes" AS "l" 
                    WHERE 
                        "l"."post_id" = "Post"."id" AND 
                        "l"."user_id" = ${user.id} AND
                        "l"."is_deleted" = false
                )`), 'isLiked'],
                [Sequelize.literal(`(
                    SELECT COUNT(*) 
                    FROM "Donators" AS "d" 
                    WHERE 
                        "d"."post_id" = "Post"."id" AND 
                        "d"."is_deleted" = false
                )`), 'donatorCount'],
                [Sequelize.literal(`(
                    SELECT COUNT(*) > 0 
                    FROM "Donators" AS "d" 
                    WHERE 
                        "d"."post_id" = "Post"."id" AND 
                        "d"."user_id" = ${user.id} AND
                        "d"."is_deleted" = false
                )`), 'isDonatord'],
                [Sequelize.literal(`(
                    SELECT COALESCE(SUM(CAST("d"."donatedAmount" AS DECIMAL)), 0)
                    FROM "Donators" AS "d"
                    WHERE 
                        "d"."post_id" = "Post"."id" AND 
                        "d"."is_deleted" = false
                )`), 'totalDonatedAmount'],
                [Sequelize.literal(`(
                    SELECT COALESCE(SUM(CAST("d"."donatedAmount" AS DECIMAL)), 0)
                    FROM "Donators" AS "d"
                    WHERE 
                        "d"."post_id" = "Post"."id" AND 
                        "d"."is_deleted" = false AND
                        "d"."user_id" = ${user.id}
                )`), 'myContributionAmount']
            ],
            include: [
                {
                    model: User,
                    as: 'user',
                    attributes: ["id", "username", "email", "profile_picture_url", "privacy_status", "first_name", "last_name"]
                },
                {
                    model: Tag,
                    as: 'tags',
                    where: { is_deleted: false },
                    required: false,
                    include: [{
                        model: User,
                        as: 'taggedUser',
                        attributes: ['id', 'username', 'first_name', 'last_name']
                    }]
                },
                {
                    model: Group,
                    as: 'group',
                    attributes: ["id", "group_name"]
                },
                {
                    model: Donator,
                    as: 'donators',
                    where: { is_deleted: false },
                    required: false,
                    include: [{
                        model: User,
                        as: 'user',
                        attributes: ['id', 'username', 'first_name', 'last_name', 'profile_picture_url']
                    }]
                }
            ],
            order: [['created_at', 'DESC']],
            limit,
            offset,
        });

        // When getting friend statuses for posts, use the new helper function
        let postsWithFriendStatus = [];
        try {
            postsWithFriendStatus = await Promise.all(postDocs.rows.map(async (post) => {
                try {
                    const friendStatuses = await getFriendStatuses(user.id, post.user_id);
                    return {
                        ...post.toJSON(),
                        ...friendStatuses
                    };
                } catch (error) {
                    console.error(`Error getting friend status for post ${post.id}:`, error);
                    return {
                        ...post.toJSON(),
                        myStatus: null,
                        friendStatus: null,
                        isFriend: false
                    };
                }
            }));
        } catch (error) {
            console.error('Error processing friend statuses:', error);
            // Continue with posts without friend status
            postsWithFriendStatus = postDocs.rows.map(post => post.toJSON());
        }

        // Cache the results
        try {
            await cachePosts(postsListKey, countKey, postsWithFriendStatus, postDocs.count);
        } catch (error) {
            console.error('Error caching posts:', error);
            // Continue even if caching fails
        }

        return {
            total: postDocs.count,
            posts: postsWithFriendStatus,
            page,
            limit
        };
    } catch (error) {
        console.error('Error in getPosts:', error);
        // Provide more detailed error information
        if (error instanceof ApiError) {
            throw error;
        }
        throw new ApiError(
            httpStatus.INTERNAL_SERVER_ERROR,
            `Error fetching posts: ${error.message || 'Unknown error'}`
        );
    }
};

// Helper functions for filtering
const getFriendIds = async (userId) => {
    try {
        const friends = await Friend.getFriendsWithStatus(userId, 'accepted', 0, 1000); // Increased limit to get all friends
        return friends.map(friend => parseInt(friend.id, 10));
    } catch (error) {
        console.error('Error getting friend IDs:', error);
        return [];
    }
};

const getGroupIds = async (userId) => {
    try {
        const groups = await Group_member.findAll({
            where: { user_id: userId }
        });
        return groups.map(g => g.group_id);
    } catch (error) {
        console.error('Error getting group IDs:', error);
        return [];
    }
};

// Get friend statuses for all posts using Neo4j
const getFriendStatuses = async (userId, postUserId) => {
    try {
        const [myStatus, friendStatus] = await Promise.all([
            Friend.getFriendshipStatus(userId, postUserId),
            Friend.getFriendshipStatus(postUserId, userId)
        ]);
        
        return {
            myStatus: myStatus?.status || null,
            friendStatus: friendStatus?.status || null,
            isFriend: myStatus?.status === 'accepted'
        };
    } catch (error) {
        console.error('Error getting friend statuses:', error);
        return {
            myStatus: null,
            friendStatus: null,
            isFriend: false
        };
    }
};

// Get blocked users with error handling
const getBlockedUsers = async (userId) => {
    try {
        const blockedUsers = await Friend.getFriendsWithStatus(userId, 'blocked', 0, 1000);
        return blockedUsers.map(friend => parseInt(friend.id, 10));
    } catch (error) {
        console.error('Error getting blocked users:', error);
        return [];
    }
};

// Cache operations with error handling
const cachePosts = async (postsListKey, countKey, posts, count) => {
    try {
        const postsHash = {};
        posts.forEach(post => {
            postsHash[post.id] = post;
        });
        
        // Store posts in hash
        for (const [postId, post] of Object.entries(postsHash)) {
            await redisSetHash(postsListKey, postId, post);
        }
        
        // Store count
        await redisSet(countKey, count);
    } catch (error) {
        console.error('Error caching posts:', error);
        // Continue execution even if caching fails
    }
};

const getPost = async ({id}, user) => {
    // Try to get from cache first
    const cacheKey = getPostKey(id);
    const cachedPost = await redisGet(cacheKey);
    
    if (cachedPost) {
        return cachedPost;
    }

    const post = await Post.findOne({
        where: { id, is_deleted: false },
        attributes: [
            "id",
            "user_id",
            "content",
            "media_url",
            "location",
            "created_at",
            "start_date",
            "end_date",
            "category",
            "donation_target",
            [Sequelize.literal('(CASE WHEN "Post"."user_id" = :userId THEN true ELSE false END)'), 'isOwn'],
            [Sequelize.literal(`(
                SELECT COUNT(*) 
                FROM "Likes" AS "l" 
                WHERE 
                    "l"."post_id" = "Post"."id" AND 
                    "l"."is_deleted" = false AND
                    "l"."comment_id" IS NULL
            )`), 'likeCount'],
            [Sequelize.literal(`(
                SELECT COUNT(*) 
                FROM "Comments" AS "c" 
                WHERE 
                    "c"."post_id" = "Post"."id" AND 
                    "c"."is_deleted" = false
            )`), 'commentCount'],
            [Sequelize.literal(`(
                SELECT COUNT(*) > 0 
                FROM "Likes" AS "l" 
                WHERE 
                    "l"."post_id" = "Post"."id" AND 
                    "l"."user_id" = :userId AND
                    "l"."is_deleted" = false
            )`), 'isLiked'],
            [Sequelize.literal(`(
                SELECT COUNT(*) 
                FROM "Donators" AS "d" 
                WHERE 
                    "d"."post_id" = "Post"."id" AND 
                    "d"."is_deleted" = false
            )`), 'donatorCount'],
            [Sequelize.literal(`(
                SELECT COUNT(*) > 0 
                FROM "Donators" AS "d" 
                WHERE 
                    "d"."post_id" = "Post"."id" AND 
                    "d"."user_id" = :userId AND
                    "d"."is_deleted" = false
            )`), 'isDonatord'],
            [Sequelize.literal(`(
                SELECT COALESCE(SUM(CAST("d"."donatedAmount" AS DECIMAL)), 0)
                FROM "Donators" AS "d"
                WHERE 
                    "d"."post_id" = "Post"."id" AND 
                    "d"."is_deleted" = false
            )`), 'totalDonatedAmount'],
            [Sequelize.literal(`(
                SELECT COALESCE(SUM(CAST("d"."donatedAmount" AS DECIMAL)), 0)
                FROM "Donators" AS "d"
                WHERE 
                    "d"."post_id" = "Post"."id" AND 
                    "d"."is_deleted" = false AND
                    "d"."user_id" = :userId
            )`), 'myContributionAmount']
        ],
        include: [
            {
                model: User,
                as: 'user',
                attributes: ["id", "username", "email", "profile_picture_url", "privacy_status", "first_name", "last_name"]
            },
            {
                model: Tag,
                as: 'tags',
                where: { is_deleted: false },
                required: false,
                include: [{
                    model: User,
                    as: 'taggedUser',
                    attributes: ['id', 'username', 'first_name', 'last_name']
                }]
            },
            {
                model: Group,
                as: 'group',
                attributes: ["id", "group_name"]
            },
            {
                model: Donator,
                as: 'donators',
                where: { is_deleted: false },
                required: false,
                include: [{
                    model: User,
                    as: 'user',
                    attributes: ['id', 'username', 'first_name', 'last_name', 'profile_picture_url']
                }]
            }
        ],
    });

    if (post) {
        // Get friend status using Neo4j
        const myStatus = await Friend.getFriendshipStatus(user.id, post.user_id);
        const friendStatus = await Friend.getFriendshipStatus(post.user_id, user.id);
        const isFriend = await Friend.getFriendshipStatus(user.id, post.user_id)?.status === 'accepted';

        // Add friend status to post
        const postWithFriendStatus = {
            ...post.toJSON(),
            myStatus: myStatus?.status || null,
            friendStatus: friendStatus?.status || null,
            isFriend
        };

        // Cache the post
        await redisSet(cacheKey, postWithFriendStatus);
        
        // Also add to user's posts list
        const postsListKey = getPostsListKey(post.user_id);
        await redisSetHash(postsListKey, post.id, postWithFriendStatus);

        return postWithFriendStatus;
    }

    return post;
}

module.exports={
    addPost,
    getPosts,
    editPost,
    getPost
}