const Joi = require("joi");

module.exports.addPost = {
    body: Joi.object().keys({
        media_url: Joi.array().required(),
        content: Joi.string().required(),
        location: Joi.string().optional().allow(null, ""),
        latitude: Joi.string().optional().allow(null, ""),
        longitude: Joi.string().optional().allow(null, ""),
        user_id: Joi.number().optional().allow(null),
        group_id: Joi.number().optional().allow(null),
        tagged_user_ids: Joi.array().items(Joi.number()).optional().allow(null),
        hash_tags: Joi.array().items(Joi.string()).optional().allow(null),
        is_donation: Joi.boolean(),
        start_date: Joi.string().optional().allow(null, ""),
        end_date: Joi.string().optional().allow(null, ""),
        category: Joi.string().optional().allow(null, ""),
        donation_target: Joi.string().optional().allow(null, ""),
    }),
};
module.exports.editPost = {
    body: Joi.object().keys({
        post_id: Joi.number().required(),
        media_url: Joi.array().required(),
        content: Joi.string().optional().allow(null, ""),
        location: Joi.string().optional().allow(null, ""),
        latitude: Joi.string().optional().allow(null, ""),
        longitude: Joi.string().optional().allow(null, ""),
        user_id: Joi.number().optional().allow(null, ""),
        is_deleted: Joi.boolean().optional().allow(null, ""),
        hash_tags: Joi.array().items(Joi.string()).optional().allow(null),
        is_donation: Joi.boolean(),
        start_date: Joi.string().optional().allow(null, ""),
        end_date: Joi.string().optional().allow(null, ""),
        category: Joi.string().optional().allow(null, ""),
        donation_target: Joi.string().optional().allow(null, ""),
    }),
};
module.exports.getPosts = {
    body: Joi.object().keys({
        filters: Joi.object().required(),
        page: Joi.number().required(),
        limit: Joi.number().required()
    }),
};