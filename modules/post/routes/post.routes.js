const express = require("express");
const router = express.Router();
const validate = require("../../../helpers/validate");
const postValidation = require("../validations/post.validations");
const postController = require("../controllers/post.controller");
const { authorize } = require("../../../authwires/auth");

router.use(authorize)

/**
 * @swagger
 * /post/add-post:
 *   post:
 *     summary: add post
 *     tags:
 *       - Post
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: body
 *         in: body
 *         required: true
 *         schema:
 *           type: object
 *           required:
 *             - content
 *           properties:
 *             media_url:
 *               type: array
 *               items:
 *                type: object
 *                properties:
 *                  media_type:
 *                    type: string
 *                  url:
 *                    type: string
 *             content:
 *               type: string
 *               description: Post content
 *             location:
 *               type: string
 *               description: location
 *             latitude:
 *               type: string
 *               description: latitude
 *             longitude:
 *               type: string
 *               description: longitude
 *             group_id:
 *               type: number
 *               description: Group ID if posting to a group
 *             tagged_user_ids:
 *               type: array
 *               items:
 *                 type: number
 *               description: Array of user IDs to tag in the post
 *             hash_tags:
 *               type: array
 *               items:
 *                 type: string
 *                 description: only name do not share hash id
 *             is_donation:
 *               type: boolean
 *               description: if true then post is donation campaign
 *             start_date:
 *               type: string
 *             end_date:
 *               type: string
 *             category:
 *               type: string
 *             donation_target:
 *               type: string
 *     responses:
 *       200:
 *         description: Post created successfully
 *       400:
 *         description: Bad Request
 *       500:
 *         description: Server Error
 */

router.post('/add-post', validate(postValidation.addPost), postController.addPost)
/**
 * @swagger
 * /post/edit-post:
 *   post:
 *     summary: edit post
 *     tags:
 *       - Post
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               post_id:
 *                 type: number
 *               user_id:
 *                 type: number
 *                 description: Display name for the post
 *               content:
 *                 type: string
 *                 description: Post of the user
 *               location:
 *                 type: string
 *                 description: location
 *               latitude:
 *                 type: string
 *                 description: latitude
 *               longitude:
 *                 type: string
 *                 description: longitude
 *               media_url:
 *                 type: array
 *                 items:
 *                  type: object
 *                  properties:
 *                    media_type:
 *                      type: string
 *                    url:
 *                      type: string
 *               is_deleted:
 *                 type: boolean
 *               hash_tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                   description: only name do not share hash id
 *               start_date:
 *                 type: string
 *               end_date:
 *                 type: string
 *               category:
 *                 type: string
 *               donation_target:
 *                 type: string
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/edit-post', validate(postValidation.editPost), postController.editPost)



// authorize routes
// router.use(authorize)



/**
 * @swagger
 * /post/get-posts:
 *   post:
 *     summary: Get posts with filters
 *     tags:
 *       - Post
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Post filter parameters
 *           required: true
 *           schema:
 *             type: object
 *             properties:
 *               filters:
 *                 type: object
 *                 description: Filter criteria for posts
 *                 properties:
 *                   user_id:
 *                     type: number
 *                     description: Filter posts by specific user
 *                   group_id:
 *                     type: number
 *                     description: Filter posts by specific group
 *                   friend_only:
 *                     type: boolean
 *                     description: Filter posts from friends only
 *                   media_type:
 *                     type: string
 *                     description: Filter by media type (image, video)
 *               page:
 *                 type: number
 *                 description: Page number for pagination
 *               limit:
 *                 type: number
 *                 description: Number of posts per page
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 * 
 */


router.post('/get-posts', validate(postValidation.getPosts), postController.getPosts)


/**
 * @swagger
 * /post/get-post-by-id:
 *   get:
 *     summary: Profile Details
 *     tags:
 *       - Post
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: id
 *         in: query
 *         description: The type of the file
 *         required: false
 *         schema:
 *           type: string
 *     responses:
 *        200:
 *          description: Profile Details Fetched Successfully
 *        400:
 *          description: Bad Request
 */

router.get('/get-post-by-id', postController.getPost)
// router.post('/get-post', validate(postValidation.getPost), postController.getPost)
// router.post('/delete-post', validate(postValidation.deletePost), postController.deletePost)


module.exports = router;
