const express = require("express");
const router = express.Router();
const validate = require("../../../helpers/validate");
const userValidation = require("../validations/user.validations");
const userController = require("../controllers/user.controller");
const { upload } = require("../../aws/services/s3.service");
const { authorize } = require("../../../authwires/auth");

/**
 * @swagger
 * /user/countries:
 *   get:
 *     summary: Profile Details
 *     tags:
 *       - User
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: name
 *         in: query
 *         description: The type of the file
 *         required: false
 *         schema:
 *           type: string
 *     responses:
 *        200:
 *          description: Profile Details Fetched Successfully
 *        400:
 *          description: Bad Request
 */
router.get("/countries",validate(userValidation.getCountries), userController.getCountries);

// authorize routes
router.use(authorize)
/**
 * @swagger
 * /user/add-edit-details:
 *   post:
 *     summary: Add or edit user details
 *     tags:
 *       - User
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: body
 *         in: body
 *         description: User details
 *         required: true
 *         schema:
 *           type: object
 *           properties:
 *             first_name:
 *               type: string
 *               description: User's first name (optional)
 *               nullable: true
 *             last_name:
 *               type: string
 *               description: User's last name (optional)
 *               nullable: true
 *             date_of_birth:
 *               type: string
 *               description: User's date of birth (optional)
 *               nullable: true
 *             profile_picture_url:
 *               type: string
 *               description: URL of user's profile picture (optional)
 *               nullable: true
 *             cover_picture_url:
 *               type: string
 *               description: URL of user's cover picture (optional)
 *               nullable: true
 *             bio:
 *               type: string
 *               description: Short biography of the user (optional)
 *               nullable: true
 *             gender:
 *               type: string
 *               description: User's gender (optional, must be one of the valid genders)
 *               enum: [male, female, other]
 *               nullable: true
 *             privacy_status:
 *               type: string
 *             is_tc_agreed:
 *               type: boolean
 *               default: false
 *             location:
 *               type: string
 *               description: User's location (optional)
 *               nullable: true
 *             country:
 *               type: string
 *               description: User's country (optional)
 *               nullable: true
 *             language:
 *               type: string
 *               description: User's preferred language (optional)
 *               nullable: true
 *             locationLatitude:
 *                type: string
 *             locationLongitude:
 *                type: string
 *     responses:
 *       200:
 *         description: User details updated successfully.
 *       400:
 *         description: Bad Request
 *       500:
 *         description: Server Error
 */

// Handle user details update
router.post('/add-edit-details', validate(userValidation.addDetails), userController.addDetails);



/**
 * @swagger
 * /user/profile:
 *   get:
 *     summary: Profile Details
 *     tags:
 *       - User
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: id
 *         in: query
 *         description: The type of the file
 *         required: false
 *         schema:
 *           type: string
 *     responses:
 *        200:
 *          description: Profile Details Fetched Successfully
 *        400:
 *          description: Bad Request
 */

router.get('/profile', userController.profile)
// router.post('/get-user', validate(userValidation.getUser), userController.getUser)
// router.post('/delete-user', validate(userValidation.deleteUser), userController.deleteUser)


/**
 * @swagger
 * /user/get-all-users:
 *   post:
 *     summary: Get all users with filters
 *     tags:
 *       - User
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: User filter parameters
 *           required: true
 *           schema:
 *             type: object
 *             properties:
 *               filters:
 *                 type: object
 *                 description: Filter criteria for users
 *                 properties:
 *                   isNotFriends:
 *                     type: boolean
 *                     description: When true, excludes current user and their friends from results
 *                   role:
 *                     type: number
 *                     description: Filter by user role
 *                   location:
 *                     type: string
 *                     description: Filter by user location
 *                   country:
 *                     type: string
 *                     description: Filter by user country
 *               page:
 *                 type: number
 *                 description: Page number for pagination
 *               limit:
 *                 type: number
 *                 description: Number of users per page
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/get-all-users', validate(userValidation.getAllUsers), userController.getAllUsers)

/**
 * @swagger
 * /user/edit-mobile-email:
 *   post:
 *     summary: edit email phone
 *     tags:
 *       - User
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               user_id:
 *                 type: number
 *                 description: Display name for the tag
 *               email:
 *                 type: string
 *                 description: Tag of the user
 *               mobile_number:
 *                 type: string
 *                 description: Tag of the user
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post("/edit-mobile-email", validate(userValidation.editMobileEmail) , userController.editMobileEmail)


/**
 * @swagger
 * /user/locations:
 *   get:
 *     summary: Profile Details
 *     tags:
 *       - User
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: input
 *         in: query
 *         description: The type of the file
 *         required: false
 *         schema:
 *           type: string
 *     responses:
 *        200:
 *          description: Profile Details Fetched Successfully
 *        400:
 *          description: Bad Request
 */


router.get('/locations', validate(userValidation.getPlacesSuggestions ) , userController.getPlacesSuggestions );


/**
 * @swagger
 * /user/add-fcm-token:
 *   post:
 *     summary: Add FCM token
 *     tags:
 *       - User
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: body
 *         in: body
 *         description: FCM token
 *         required: true
 *         schema:
 *           type: object
 *           properties:
 *             fcmToken:
 *               type: string
 *               description: FCM token
 *     responses:
 *        200:
 *          description: FCM token added successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/add-fcm-token', validate(userValidation.addUserFcmToken) , userController.addUserFcmToken);
module.exports = router;
