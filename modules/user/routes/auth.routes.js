const express = require("express");
const router = express.Router();
const validate = require("../../../helpers/validate");
const  authValidation  = require("../validations/auth.validations");
const authController = require("../controllers/auth.controller");
const multer = require('multer');
const { storage } = require("../../../configuration/storage");
const { authorize } = require("../../../authwires/auth");
const upload = multer({ storage: storage });
const passport = require("passport");

/**
 * @swagger
 * /auth/register:
 *   post:
 *     summary: register user
 *     tags:
 *       - Auth
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: false
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *                  username:
 *                    type: string
 *                    description: User name (optional, can be null or empty string)
 *                    nullable: true
 *                  email:
 *                    type: string
 *                    description: User mobile_number (optional, can be null or empty string)
 *                    nullable: true
 *                  password:
 *                    type: string
 *                    description: User email (optional, can be null or empty string)
 *                    nullable: true
 *                  first_name:
 *                    type: string
 *                    description: User password (optional, can be null or empty string)
 *                    nullable: true
 *                  last_name:
 *                    type: string
 *                    description: User role from role model
 *                    nullable: true
 *                  date_of_birth:
 *                    type: string
 *                    description: User role from role model
 *                    nullable: true
 *                  mobile_number:
 *                    type: string
 *                    description: User role from role model
 *                    nullable: true
 *                  gender:
 *                    type: string
 *                    description: User role from role model
 *                    nullable: true
 *                  location:
 *                    type: string
 *                    description: User role from role model
 *                    nullable: true
 *                  country:
 *                    type: string
 *                    description: User role from role model
 *                    nullable: true
 *                  language:
 *                    type: string
 *                    description: User role from role model
 *                    nullable: true
 *                  role:
 *                    type: number
 *                    description: User role from role model
 *                    nullable: true
 *                  is_tc_agreed:
 *                    type: boolean
 *                    description: User role from role model
 *                    nullable: true
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/register', validate(authValidation.register), authController.register)



// authorize routes
// router.use(authorize)



/**
 * @swagger
 * /auth/login:
 *   post:
 *     summary: login user
 *     tags:
 *       - Auth
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Login details
 *           required: true
 *           schema:
 *             type: object
 *             properties:
 *                  email:
 *                    type: string
 *                    description: email or mobile_number
 *                  password:
 *                    type: string
 *                    description: password
 *     responses:
 *        200:
 *          description: Loggedin successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/login', validate(authValidation.login), authController.login)



/**
 * @swagger
 * /auth/verify-credential:
 *   post:
 *     summary: verify user credentials
 *     tags:
 *       - Auth
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Login details
 *           required: true
 *           schema:
 *             type: object
 *             properties:
 *                  email:
 *                    type: string
 *                    description: email or mobile_number
 *                  otp:
 *                    type: string
 *                    description: otp
 *                  verifying:
 *                    type: string
 *     responses:
 *        200:
 *          description: Loggedin successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/verify-credential', validate(authValidation.verifyCredential), authController.verifyCredential)

/**
 * @swagger
 * /auth/verify-username:
 *   post:
 *     summary: verify user usernames
 *     tags:
 *       - Auth
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Login details
 *           required: true
 *           schema:
 *             type: object
 *             properties:
 *                  user_id:
 *                    type: string
 *                    description: user_id or mobile_number
 *                  username:
 *                    type: string
 *                    description: username
 *     responses:
 *        200:
 *          description: Loggedin successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/verify-username', validate(authValidation.verifyUsername), authController.verifyUsername)

/**
 * @swagger
 * /auth/google-mobile:
 *   post:
 *     summary: verify user credentials
 *     tags:
 *       - Auth
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Login details
 *           required: true
 *           schema:
 *             type: object
 *             properties:
 *                  idToken:
 *                    type: string
 *                    description: email or mobile_number
 *     responses:
 *        200:
 *          description: Loggedin successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/google-mobile', validate(authValidation.googleMobile), authController.googleMobile)
/**
 * @swagger
 * /auth/facebook-mobile:
 *   post:
 *     summary: verify user credentials
 *     tags:
 *       - Auth
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Login details
 *           required: true
 *           schema:
 *             type: object
 *             properties:
 *                  accessToken:
 *                    type: string
 *                    description: email or mobile_number
 *     responses:
 *        200:
 *          description: Loggedin successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/facebook-mobile', validate(authValidation.facebookMobile), authController.facebookMobile)


/**
 * @swagger
 * /auth/forget-password:
 *   post:
 *     summary: reset password otp sent
 *     tags:
 *       - Auth
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Login details
 *           required: true
 *           schema:
 *             type: object
 *             properties:
 *                  email:
 *                    type: string
 *                    description: email or mobile_number
 *     responses:
 *        200:
 *          description: Loggedin successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/forget-password', validate(authValidation.forgetPassword), authController.forgetPassword)
/**
 * @swagger
 * /auth/send-otp:
 *   post:
 *     summary: reset password otp sent
 *     tags:
 *       - Auth
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Login details
 *           required: true
 *           schema:
 *             type: object
 *             properties:
 *                  email:
 *                    type: string
 *                    description: email or mobile_number
 *                  mobile_number:
 *                    type: string
 *     responses:
 *        200:
 *          description: Loggedin successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/send-otp', validate(authValidation.sendOtp), authController.sendOtp)



/**
 * @swagger
 * /auth/validate-otp:
 *   post:
 *     summary: validate reset password otp
 *     tags:
 *       - Auth
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Login details
 *           required: true
 *           schema:
 *             type: object
 *             properties:
 *                  token:
 *                    type: string
 *                  otp:
 *                    type: string
 *                    description: otp
 *     responses:
 *        200:
 *          description: Loggedin successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/validate-otp', validate(authValidation.validateOtp), authController.validateOtp)

/**
 * @swagger
 * /auth/reset-password:
 *   post:
 *     summary: change password
 *     tags:
 *       - Auth
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Login details
 *           required: true
 *           schema:
 *             type: object
 *             properties:
 *                  token:
 *                    type: string
 *                  otp:
 *                    type: string
 *                    description: otp
 *                  password:
 *                    type: string
 *                    description: password
 *     responses:
 *        200:
 *          description: Loggedin successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/reset-password', validate(authValidation.resetPassword), authController.resetPassword)

/**
 * @swagger
 * /auth/google-login:
 *   get:
 *     summary: change password
 *     tags:
 *       - Auth
 *     produces:
 *       - application/json
 *     parameters:
 *     responses:
 *        200:
 *          description: Loggedin successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


  
  // Callback route after Google authentication
  router.get(
    "/google-callback",
    passport.authenticate("google", { session: false }),
    authController.googleLogin
  );
/**
 * @swagger
 * /auth/facebook-login:
 *   get:
 *     summary: change password
 *     tags:
 *       - Auth
 *     produces:
 *       - application/json
 *     parameters:
 *     responses:
 *        200:
 *          description: Loggedin successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


  
  // Callback route after Google authentication
  router.get(
    "/facebook-callback",
    passport.authenticate("facebook", { session: false }), // No session
    authController.facebookLogin
    // (req, res) => {
    //   // Send back only the profile data
    //   res.json({
    //     id: req.user.id,
    //     name: req.user.displayName,
    //     email: req.user.emails ? req.user.emails[0].value : "N/A",
    //     profilePicture: req.user.photos ? req.user.photos[0].value : "N/A"
    //   });
    // }
  );

/**
 * @swagger
 * /auth/phone-login:
 *   post:
 *     summary: Login with phone number
 *     tags:
 *       - Auth
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: body
 *         in: body
 *         description: Phone login details
 *         required: true
 *         schema:
 *           type: object
 *           required:
 *             - mobile_number
 *           properties:
 *             mobile_number:
 *               type: string
 *               description: User's mobile number
 *     responses:
 *       200:
 *         description: OTP sent successfully
 *       400:
 *         description: Bad Request
 *       404:
 *         description: User not found
 *       500:
 *         description: Server Error
 */
router.post('/phone-login', validate(authValidation.phoneLogin), authController.phoneLogin);

/**
 * @swagger
 * /auth/verify-phone-login:
 *   post:
 *     summary: Verify phone login OTP
 *     tags:
 *       - Auth
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: body
 *         in: body
 *         description: Phone verification details
 *         required: true
 *         schema:
 *           type: object
 *           required:
 *             - mobile_number
 *             - otp
 *           properties:
 *             mobile_number:
 *               type: string
 *               description: User's mobile number
 *             otp:
 *               type: string
 *               description: OTP received on mobile
 *             token:
 *               type: string
 *     responses:
 *       200:
 *         description: Login successful
 *       400:
 *         description: Bad Request
 *       404:
 *         description: User not found
 *       500:
 *         description: Server Error
 */
router.post('/verify-phone-login', validate(authValidation.verifyPhoneLogin), authController.verifyPhoneLogin);

router.use(authorize)

/**
 * @swagger
 * /auth/logout:
 *   get:
 *     summary: Profile Details
 *     tags:
 *       - Auth
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     responses:
 *        200:
 *          description: logout
 *        400:
 *          description: Bad Request
 */

router.get('/logout', authController.logout)


module.exports = router;
