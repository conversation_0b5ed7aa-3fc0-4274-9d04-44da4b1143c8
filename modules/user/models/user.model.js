'use strict';

const bcrypt = require('bcryptjs');
const {userStatuses , userGenders} = require("../../../configuration/constant")


module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define('User', {

    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true
    },
    password_hash: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    first_name: {
      type: DataTypes.STRING(50),
    },
    last_name: {
      type: DataTypes.STRING(50),
    },
    date_of_birth: {
      type: DataTypes.DATE,
    },
    profile_picture_url: {
      type: DataTypes.TEXT,
    },
    cover_picture_url: {
      type: DataTypes.TEXT,
    },
    bio: {
      type: DataTypes.TEXT,
    },
    mobile_number: {
      type: DataTypes.STRING,
    },
    last_password_hash: {
      type: DataTypes.STRING(255),
    },
    otp: {
      type: DataTypes.STRING(6),
    },
    gender: {
      type: DataTypes.STRING,
      enum: Object.values(userGenders)
    },
    role: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Roles',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT'
    },
    privacy_status: {
      type: DataTypes.ENUM('private', 'public', 'followersOnly'),
      defaultValue: 'public'
    },
    is_tc_agreed: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    google_id: {
      type: DataTypes.TEXT,
    },
    apple_id: {
      type: DataTypes.TEXT,
    },
    facebook_id: {
      type: DataTypes.TEXT,
    },
    
    location: {
      type: DataTypes.STRING,
    },
    country: {
      type: DataTypes.STRING,
    },
    language: {
      type: DataTypes.STRING,
    },
    locationLatitude:{
      type: DataTypes.STRING,
    },
    locationLongitude:{
      type: DataTypes.STRING,
    },
    is_email_verified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    is_mobile_verified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    status: {
      type: DataTypes.STRING,
      enum: Object.values(userStatuses)
    },
    last_login_at: {
      type: DataTypes.DATE,
    },
    fcmToken: {
      type: DataTypes.STRING,
      allowNull: true
    },
    is_deleted:{
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    deleted_at: {
      type: DataTypes.DATE,
    },
    created_at:{
      type: DataTypes.DATE
    },
    updated_at:{
      type: DataTypes.DATE
    },
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    tableName: 'Users',
    hooks: {
      beforeSave: async (user) => {
        if (user.changed('password_hash')) {
          user.password_hash = await bcrypt.hash(user.password_hash, 10);
        }
      }
    }
  });

  User.prototype.isPasswordCorrect = async function (password) {
    return await bcrypt.compare(password, this.password_hash);
  };
  User.associate = (models) => {
    User.belongsTo(models.Role, { 
      foreignKey: 'role',
      as: 'userRole',
      targetKey: 'id'
    });
  };
  
  return User;
};
