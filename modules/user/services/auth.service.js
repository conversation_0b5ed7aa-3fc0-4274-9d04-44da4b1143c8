// const { User, Token, Role } = require("../models");
const { User, Token } = require("../../../models");
const httpStatus = require("http-status");
const ApiError = require("../../../utils/ApiError");
const {
  generateMailVerificationToken,
  generateOTP,
  generateAuthToken,
  generateresetPasswordToken,
  generatemobile_numberVerificationToken,
} = require("../../token/services/token.service");
const bcrypt = require("bcryptjs");
const { sendEmail } = require("../../../utils/email");
const {
  tokenTypes,
  userStatuses,
  userGenders,
} = require("../../../configuration/constant");
const { Op, where } = require("sequelize");
const { OAuth2Client } = require("google-auth-library");
const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);
const axios = require("axios");
const jwt = require("jsonwebtoken");
const { sendSms } = require("../../../helpers/sms");
const { phone_verification, reset_password } = require("../../../utils/sms");

const register = async (reqBody) => {
  let message = "Registration Successful";
  Object.keys(reqBody).forEach((key) =>
    reqBody[key] === "" ? delete reqBody[key] : null
  );

  let user = await User.findOne({
    where: {
      is_deleted: false,
      [Op.or]: [
        { email: reqBody.email },
        { mobile_number: reqBody.mobile_number },
        { username: reqBody.username },
      ],
    },
  });
  user = (user && user.dataValues) || null;

  if (user?.isBlacklisted)
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Your account is suspended, please contact admin"
    );
  if (user && (user.is_email_verified || user.is_mobile_verified))
    throw new ApiError(httpStatus.CONFLICT, "User already registered");

  if (user && !user.is_email_verified && !user.is_mobile_verified) {
    await Token.destroy({ where: { user_id: user.id } });
    reqBody.email
      ? await Token.destroy({ where: { email: reqBody.email } })
      : await Token.destroy({
          where: { mobile_number: reqBody.mobile_number },
        });
    await User.destroy({ where: { id: user.id } });
  }

  user = await User.create({ ...reqBody, password_hash: reqBody.password });
  // console.log("hereeeeeeeeeeeeeeeeeeeeeeeeee", user);
  const otp = generateOTP();
  const token = 
  reqBody.email? 
    await generateMailVerificationToken(reqBody.email, otp) 
  : await generatemobile_numberVerificationToken(reqBody.mobile_number, otp)
  ;

  reqBody.email?
    await sendEmail(
      reqBody.email.toLowerCase(),
      "Verification Mail || Pippan",
      "verification-mail",
      {
        otp,
        token: token.token,
      }
    ) : await sendSms(phone_verification(otp), reqBody.mobile_number)

  return { message, data: user };
};

const login = async ({ email, password }) => {
  let message = "Login Successful";
  const user = await User.findOne({
    where: {
      is_deleted: false,
      [Op.or]: [{ email: email || "" }, { mobile_number: email || "" }],
    },
  });

  if (!user) throw new ApiError(httpStatus.BAD_REQUEST, "No user found");
  if (!user.is_email_verified && !user.is_mobile_verified)
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Please verify your email or mobile_number"
    );
  if (user.isBlacklisted)
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Your account is suspended, please contact admin"
    );
  if (!(await bcrypt.compare(password, user.password_hash)))
    throw new ApiError(httpStatus.BAD_REQUEST, "Invalid login credentials");

  await user.update({ last_login_at: new Date() });

  const token = await generateAuthToken(user);
  return { message, user, token };
};

const verifyCredential = async ({ email, otp , verifying }) => {
  let message = verifying === "mobile_number"?"Phone":"Email"+" Verified Successfully";
  verifying = !verifying || verifying === ""?"email":"mobile_number";
  let query = verifying === "mobile_number"?{otp}:{otp}
  let token = await Token.findOne({
    where: {
      [Op.or]: [{ email: email || "" }, { mobile_number: email || "" }],
      ...query,
    },
  });

  if (!token) throw new ApiError(httpStatus.BAD_REQUEST, "Wrong OTP entered");
  let user = await User.findOne({
    where: {
      is_deleted: false,
      [Op.or]: [{ email: email || "" }, { mobile_number: email || "" }],
    },
  });
  if (!user) throw new ApiError(httpStatus.BAD_REQUEST, "No user found");

  verifying === "email"
    ? await user.update({ is_email_verified: true, last_login_at: new Date() })
    : await user.update({
        is_mobile_verified: true,
        last_login_at: new Date(),
      });

  await Token.destroy({
    where: {
      [Op.or]: [{ email: email || "" }, { mobile_number: email || "" }],
      otp,
    },
  });
  token = await generateAuthToken(user);

  return token ? { message, user, token } : { message, user };
};


const generateUniqueUsernames = async (baseUsername) => {
  const suggestions = [];
  const randomNumbers = [123, 456, 789, Math.floor(1000 + Math.random() * 9000)];

  for (let num of randomNumbers) {
      let newUsername = `${baseUsername}${num}`;
      const existingUser = await User.findOne({ where: { username: newUsername } });

      if (!existingUser) {
          suggestions.push(newUsername);
      }

      if (suggestions.length >= 3) break; // Return max 3 suggestions
  }

  return suggestions;
}

const verifyUsername = async ({ user_id, username }) => {
  const existingUser = await User.findOne({
    where: {
      username: username,
      ...(user_id && { id: { [Op.ne]: user_id } }), // Exclude current user if updating profile
    },
  });

  if (!existingUser) {
    return {
      available: true,
      message: "Username is available, you can go with the username.",
    };
  }

  // If username is taken, generate suggestions
  const suggestions = await generateUniqueUsernames(username);

  return {
    available: false,
    message: "Username is already taken.",
    suggestions: suggestions,
  };
};

const googleMobile = async ({ idToken }) => {
  // Verify Google ID Token
  const message = "Login Successfull";
  const ticket = 
  jwt.decode(idToken, { complete: true })
  
  // await client.verifyIdToken({
  //   idToken,
  //   audience: "706590898626-bklpv6ie8ljmms6heavn720j6fudjqoh.apps.googleusercontent.com"
  //   // process.env.GOOGLE_MOBILE_ID,
  // });

  const payload = ticket.payload; // Extract user data
  // return payload


  const user = {
    id: payload.sub,
    name: payload.name,
    email: payload.email,
    picture: payload.picture,
  };
  // return user

  let userDoc = await User.findOne({ where: { email: user.email } });
  console.log(userDoc)
  if (userDoc) {
    await userDoc.update({
      username:userDoc.username || user.email,
      email: user.email,
      first_name: user.name.split(" ")[0],
      last_name: user.name.split(" ").length > 1? user.name.split(" ")[user.name.split(" ").length -1] : userDoc.name,
      profile_picture_url: user.picture || userDoc.profile_picture_url,
      is_tc_agreed: true,
      google_id: user.id,
      is_email_verified: true,
      status: userStatuses.ACTIVE,
      last_login_at: new Date(),
    });
    // token = await Token
  } else {
    // console.log(user.email)
    userDoc = await User.create({
      username: user.email,
      email: user.email,
      first_name: user.name.split(" ")[0],
      last_name: user.name.split(" ").length > 1? user.name.split(" ")[user.name.split(" ").length -1] : "",
      profile_picture_url: user.picture || "",
      is_tc_agreed: true,
      google_id: user.id,
      is_email_verified: true,
      status: userStatuses.ACTIVE,
      last_login_at: new Date(),
    });
  }
  // console.log("userDoc-----------------------------------------",userDoc)

  const token = await generateAuthToken(userDoc);
  // return userDoc

  return { message, user:userDoc.dataValues, token };
};
const facebookMobile = async ({ accessToken }) => {
  // Verify Google ID Token
  const message = "Login Successfull";
  const response = await axios.get(
    `https://graph.facebook.com/me?fields=id,name,email,picture.type(large)&access_token=${accessToken}`
  );

  // return response.data

  const user = {
    id: response.data.id,
    name: response.data.name,
    email: response.data.email,
    picture: response.data.picture.data.url,
  };

  const userDoc = await User.findOne({ where: { email: user.email } });
  if (userDoc) {
    await userDoc.update({
      username:userDoc.username || user.email,
      email: user.email,
      first_name: user.given_name,
      last_name: user.family_name || userDoc.family_name,
      profile_picture_url: user.picture || userDoc.profile_picture_url,
      is_tc_agreed: true,
      facebook_id: user.id,
      is_email_verified: true,
      status: userStatuses.ACTIVE,
      last_login_at: new Date(),
    });
    // token = await Token
  } else {
    await User.create({
      username: user.email,
      email: user.email,
      first_name: user.given_name,
      last_name: user.family_name || "",
      profile_picture_url: user.picture || "",
      is_tc_agreed: true,
      facebook_id: user.id,
      is_email_verified: true,
      status: userStatuses.ACTIVE,
      last_login_at: new Date(),
    });
  }
  const token = await generateAuthToken(userDoc);

  return { message, ...userDoc.dataValues, token };
};

const forgetPassword = async ({ email }) => {
  let message = "Forget Password Successful";
  const user = await User.findOne({
    where: {
      is_deleted: false,
      [Op.or]: [{ email: email || "" }, { mobile_number: email || "" }],
    },
  });
  if (!user) throw new ApiError(httpStatus.NOT_FOUND, "Credentials not found");
  const otp = generateOTP();

  const token = await generateresetPasswordToken(user.id, otp);

  if (user.email)
    await sendEmail(
      user.email.toLowerCase(),
      "Reset password Mail || Pippan",
      "reset-password-mail",
      { otp }
    );

  if(user.mobile_number)
    await sendSms(reset_password(otp), user.mobile_number)

  return { message, data: token };
};

const sendOtp = async (reqBody) =>{
  Object.entries(reqBody).forEach(([key, value]) =>{
    if(value === "") delete reqBody[key]
  })
  let user;
  if(reqBody.email ) user = await User.findOne({where: {email: reqBody.email, is_deleted: false}})
  else user = await User.findOne({where: {mobile_number: reqBody.mobile_number, is_deleted: false}})

  if(!user) throw new ApiError(httpStatus.BAD_REQUEST, "No User Found")

    if(reqBody.email){
      await Token.destroy({
        where:{
          email: reqBody.email,
          type: "mailVerify",  
        }
      })
    }else{
      await Token.destroy({
        where:{
          email: reqBody.mobile_number,
          type: "mobile_numberVerify",  
        }
      })
    }
  const otp = generateOTP();
  reqBody.email? await generateMailVerificationToken(reqBody.email, otp) : await generatemobile_numberVerificationToken(reqBody.mobile_number , otp)
  reqBody.email?  await sendEmail(
    reqBody.email.toLowerCase(),
    "Verification Mail || Pippan",
    "verification-mail",
    {
      otp,
    }
  ) : await sendSms(phone_verification(otp), reqBody.mobile_number)

  // await sendEmail(                //it will be sms when sms available
  //   reqBody.email.toLowerCase(),
  //   "Verification Mail || Pippan",
  //   "verification-mail",
  //   {
  //     otp,
  //   }
  // )
  
}

const validateOtp = async ({ otp, token }) => {
  let message = "OTP Validation Successful";
  const tokenDoc = await Token.findOne({
    // where: { type: tokenTypes.RESET_PASSWORD_OTP, token },
    where: { otp, token },
  });
  if (!tokenDoc)
    throw new ApiError(httpStatus.NOT_FOUND, "You entered a wrong OTP");
  return { message, otp };
};

const resetPassword = async ({ token, otp, password }) => {
  let message = "Password Reset Successfully";
  const tokenDoc = await Token.findOne({
    where: { otp, type: tokenTypes.RESET_PASSWORD_OTP, token },
    where: { type: tokenTypes.RESET_PASSWORD_OTP, token },
  });
  if (!tokenDoc)
    throw new ApiError(httpStatus.NOT_FOUND, "You entered a wrong OTP");

  const userDoc = await User.findOne({
    where: {
      is_deleted: false,
      id: tokenDoc.user_id,
    },
  });
  if (!userDoc) throw new ApiError(httpStatus.NOT_FOUND, "User not found");

  // Check if new password is same as old password
  const isSamePassword = await bcrypt.compare(password, userDoc.password_hash);
  if (isSamePassword) {
    throw new ApiError(httpStatus.BAD_REQUEST, "New password cannot be the same as your current password");
  }

  // password = await bcrypt.hash(password, 10);
  // const userDoc. = await User.update({ password });
  userDoc.password_hash = password;
  await userDoc.save();
  await Token.destroy({
    where: { user_id: userDoc.id, type: tokenTypes.RESET_PASSWORD_OTP },
  });

  return { message, userDoc };
};
const googleLogin = async (user) => {
  // let token;
  console.log(user)
  const userDoc = await User.findOne({ where: { email: user.email } });
  if (userDoc) {
    await userDoc.update({
      username: user.email,
      email: user.email,
      first_name: user.given_name,
      last_name: user.family_name || userDoc.family_name,
      profile_picture_url: user.picture || userDoc.profile_picture_url,
      is_tc_agreed: true,
      google_id: user.sub,
      is_email_verified: true,
      status: userStatuses.ACTIVE,
      last_login_at: new Date(),
    });
    // token = await Token
  } else {
    await User.create({
      username: user.email,
      email: user.email,
      first_name: user.given_name,
      last_name: user.family_name || "",
      profile_picture_url: user.picture || "",
      is_tc_agreed: true,
      google_id: user.sub,
      is_email_verified: true,
      status: userStatuses.ACTIVE,
      last_login_at: new Date(),
    });
  }
  const token = await generateAuthToken(userDoc);

  return { ...userDoc.dataValues, token };
};

// "result": {
//     "id": "2328164350877305",
//     "name": "Shirshendu Pal",
//     "email": "<EMAIL>",
//     "picture": {
//       "data": {
//         "height": 50,
//         "is_silhouette": false,
//         "url": "https://platform-lookaside.fbsbx.com/platform/profilepic/?asid=2328164350877305&height=50&width=50&ext=1743108863&hash=AbZqD0XSQ8C4UfBWXjxPbUKt",
//         "width": 50
//       }
//     }
//   }
const facebookLogin = async (user) => {
  // console.log(user)
  const userDoc = await User.findOne({ where: { email: user.email } });
  if (userDoc) {
    await userDoc.update({
      username: user.email,
      email: user.email,
      first_name: user.name.split(" ")[0] ? user.name.split(" ")[0] : user.name,
      last_name: user.name.split(" ")[1]
        ? user.name.split(" ")[1]
        : userDoc.family_name,
      profile_picture_url:
        user.picture && user.picture.data && user.picture.url
          ? user.picture.url
          : userDoc.profile_picture_url,
      is_tc_agreed: true,
      facebook_id: user.id,
      is_email_verified: true,
      status: userStatuses.ACTIVE,
      last_login_at: new Date(),
    });
    // token = await Token
  } else {
    await User.create({
      username: user.email,
      email: user.email,
      first_name: user.name.split(" ")[0] ? user.name.split(" ")[0] : user.name,
      last_name: user.name.split(" ")[1] ? user.name.split(" ")[1] : "",
      profile_picture_url:
        user.picture && user.picture.data && user.picture.url
          ? user.picture.url
          : "",
      is_tc_agreed: true,
      facebook_id: user.id,
      is_email_verified: true,
      status: userStatuses.ACTIVE,
      last_login_at: new Date(),
    });
  }
  const token = await generateAuthToken(userDoc);

  return { ...userDoc.dataValues, token };
};

const logout = async ({}, user) => {
  await User.update({where:{id: user.id}},{fcmToken: ""})
  await Token.destroy({ where: { token: user.token } });
  return { message: "Logout Successfully" };
};

const phoneLogin = async ({ mobile_number }) => {
  let message = "OTP sent successfully";
  
  // Find user by mobile number
  const user = await User.findOne({
    where: {
      is_deleted: false,
      mobile_number,
    },
  });

  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, "No user found with this mobile number");
  }

  if (user.isBlacklisted) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Your account is suspended, please contact admin"
    );
  }

  await Token.destroy({
    where: {
      type: tokenTypes.mobile_number_VERIFY,
      mobile_number
    }
  })
  // Generate and send OTP
  const otp = generateOTP();
  let tokenDoc=await generatemobile_numberVerificationToken(mobile_number, otp);
  await sendSms(phone_verification(otp), mobile_number);

  return { message, token: tokenDoc };
};

const verifyPhoneLogin = async ({ mobile_number, otp , token }) => {
  let message = "Login Successful";
  
  // Find verification token
  const tokenDoc = await Token.findOne({
    where: {
      mobile_number,
      token,
      otp,
      type: tokenTypes.mobile_number_VERIFY,
    },
  });

  if (!tokenDoc) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Invalid OTP");
  }

  // Find user
  const user = await User.findOne({
    where: {
      is_deleted: false,
      mobile_number,
    },
  });

  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, "No user found");
  }

  // Update user verification status and last login
  await user.update({
    is_mobile_verified: true,
    last_login_at: new Date(),
  });

  // Delete used token
  await Token.destroy({
    where: {
      mobile_number,
      type: tokenTypes.mobile_number_VERIFY,
    },
  });

  // Generate auth token
  const authToken = await generateAuthToken(user);

  return { message, user, token: authToken };
};

module.exports = {
  register,
  login,
  verifyCredential,
  forgetPassword,
  validateOtp,
  resetPassword,
  logout,
  googleLogin,
  facebookLogin,
  googleMobile,
  facebookMobile,
  verifyUsername,
  sendOtp,
  phoneLogin,
  verifyPhoneLogin
};
