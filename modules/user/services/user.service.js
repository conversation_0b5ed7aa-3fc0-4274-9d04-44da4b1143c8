// const  User  = require("../models/user.model");
// const Role  = require("../../role/models/role.model");
const { User, Role } = require("../../../models");
const Friend = require("../../friend/models/friend.model");
// const  Company = require("../../company/models/company.model");
const httpStatus = require("http-status");
const ApiError = require("../../../utils/ApiError");
// const activityService = require("../../activity/services/activity.service");
const { Sequelize, Op } = require("sequelize");
const db = require("../../../models");
const { phone_verification } = require("../../../utils/sms");
const { sendSms } = require("../../../helpers/sms");
const {
  generateOTP,
  generatemobile_numberVerificationToken,
} = require("../../token/services/token.service");
const { deleteFromS3 } = require("../../aws/services/s3.service");
const { getData } = require("country-list");

const getCountries = async ({ name }) => {
  let countries = getData();
  if (name && name !== "") {
    countries = countries.filter((country) => {
      if (
        country.code.toLowerCase().startsWith(name.trim().toLowerCase()) ||
        country.name.toLowerCase().startsWith(name.trim().toLowerCase())
      )
        return country;
    });
  }
  return countries;
};

const uploadProfilePicture = async (reqBody, user) => {
  const file = reqBody.files;
  // console.log(file)
  try {
    const uploadResult = await uploadToS3(
      file,
      `profile-pictures/${user.id}/${Date.now()}/${file.originalname}`
    );
    console.log(uploadResult);
    if (user.profile_picture_url) await deleteFromS3(user.profile_picture_url);
    const userDoc = await User.update(
      { profile_picture_url: uploadResult },
      { where: { id: user?.id }, returning: true }
    );
    // console.log(userDoc)
    return userDoc;
  } catch (error) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      "Error uploading profile picture"
    );
  }
};

const uploadCoverPicture = async (reqBody, user) => {
  const file = reqBody.files;
  try {
    const uploadResult = await uploadToS3(
      file,
      `cover-pictures/${user.id}/${Date.now()}/${file.originalname}`
    );
    if (user.cover_picture_url) await deleteFromS3(user.cover_picture_url);
    return await User.update(
      { cover_picture_url: uploadResult },
      { where: { id: user?.id }, returning: true }
    );
  } catch (error) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      "Error uploading cover picture"
    );
  }
};

const addDetails = async (reqBody, user) => {
  for (let [key, value] of Object.entries(reqBody)) {
    if (value === "") delete reqBody[key];
  }

  // Handle profile picture update
  if (reqBody.profile_picture_url && user.profile_picture_url) {
    await deleteFromS3(user.profile_picture_url);
  }

  // Handle cover picture update
  if (reqBody.cover_picture_url && user.cover_picture_url) {
    await deleteFromS3(user.cover_picture_url);
  }

  const [updatedCount, updatedUsers] = await User.update(reqBody, {
    where: { id: user?.id },
    returning: true,
  });

  if (updatedCount === 0) {
    throw new ApiError(httpStatus.NOT_FOUND, "User not found");
  }

  return updatedUsers[0];
};

const profile = async (reqQuery, user) => {
  let id = reqQuery.id || user.id;
  const userDoc = await User.findOne({
    where: { id: id },
    attributes: [
      "id",
      "username",
      "email",
      "profile_picture_url",
      "privacy_status",
      "first_name",
      "last_name",
      "date_of_birth",
      "cover_picture_url",
      "bio",
      "mobile_number",
      "gender",
      "privacy_status",
      "location",
      "country",
      "locationLatitude",
      "locationLongitude",
      "status",
      "created_at"
    ],
    include: [
      {
        model: Role,
        as: "userRole",
        attributes: ["role", "roleDisplayName", "desc"],
      },
    ],
  });

  if (!userDoc) throw new ApiError(httpStatus.NOT_FOUND, "User not found");

  // Get friend status using Neo4j
  const myStatus = await Friend.getFriendshipStatus(user.id, userDoc.id);
  const friendStatus = await Friend.getFriendshipStatus(userDoc.id, user.id);
  
  // Get follower count using Neo4j
  const followers = await Friend.getFollowers(userDoc.id);
  const followersCount = followers.length;

  // Get mutual followers using Neo4j
  const mutualFollowers = await Friend.getMutualFollowers(user.id, userDoc.id);
  const mutualFollowersCount = mutualFollowers.length;

  return {
    ...userDoc.toJSON(),
    myStatus: myStatus?.status || null,
    friendStatus: friendStatus?.status || null,
    followersCount,
    mutualFollowersCount
  };
};

const getAllUsers = async ({ filters, page, limit }, user) => {
  const offset = (page - 1) * limit;
  let whereClause = { ...filters };

  // Add search functionality
  if (filters.search) {
    delete whereClause.search;
    const searchTerm = filters.search.toLowerCase();
    whereClause = {
      ...whereClause,
      [Op.or]: [
        Sequelize.literal(`LOWER("User"."username") LIKE '%${searchTerm}%'`),
        Sequelize.literal(`LOWER("User"."first_name") LIKE '%${searchTerm}%'`),
        Sequelize.literal(`LOWER("User"."last_name") LIKE '%${searchTerm}%'`),
        Sequelize.literal(
          `LOWER(CONCAT("User"."first_name", ' ', "User"."last_name")) LIKE '%${searchTerm}%'`
        ),
      ],
    };
    delete filters.search;
  }

  if (filters.isFriendsOnly) {
    delete filters.isFriendsOnly;
    const friends = await Friend.getFriends(user.id);
    const friendIds = friends.map(f => f.id);
    whereClause.id = {
      [Op.in]: friendIds
    };
  }

  if (filters.isNotFriends) {
    const blockedUsers = await Friend.getBlockedUsers(user.id);
    const friends = await Friend.getFriends(user.id);
    const friendIds = friends.map(f => f.id);
    
    whereClause.id = {
      [Op.notIn]: [...friendIds, user.id, ...blockedUsers]
    };
    delete whereClause.isNotFriends;
  }

  if (whereClause.basedOnFollowers) {
    delete whereClause.basedOnFollowers;
    delete filters.basedOnFollowers;
    
    const blockedUsers = await Friend.getBlockedUsers(user.id);
    const myFollowers = await Friend.getFollowers(user.id);
    const myFollowings = await Friend.getFollowing(user.id);
    
    // Get followers of my followers
    const followersOfFollowers = await Promise.all(
      myFollowers.map(follower => Friend.getFollowers(follower.id))
    );
    const followersOfFollowersIds = followersOfFollowers.flat().map(f => f.id);
    
    // Get suggested users
    let suggestedUsers = [...myFollowers.map(f => f.id), ...followersOfFollowersIds]
      .filter(id => !myFollowings.some(f => f.id === id) && 
                    !blockedUsers.includes(id) && 
                    id !== user.id);

    if (suggestedUsers.length > 0) {
      whereClause = {
        ...whereClause,
        id: { [Op.in]: [...new Set(suggestedUsers)] }
      };
    } else if (blockedUsers.length > 0) {
      whereClause = {
        ...whereClause,
        id: { [Op.notIn]: [...new Set(blockedUsers)] }
      };
    } else {
      let usersFromMyCountry = await User.findAll({
        where: {
          country: user.country,
          is_email_verified: true,
          is_mobile_verified: true,
          is_tc_agreed: true,
        },
      });
      if (usersFromMyCountry.length > 0) {
        whereClause = {
          ...whereClause,
          id: { [Op.in]: usersFromMyCountry.map((u) => u.id) }
        };
      }
    }
  }

  // Apply role filter if present
  if (filters.role) {
    whereClause.role_id = filters.role;
    delete whereClause.role;
  }

  // Add group membership check if group_id is provided
  let groupMembershipAttributes = [];
  if (filters.group_id) {
    delete whereClause.group_id;
    groupMembershipAttributes = [
      [
        Sequelize.literal(`(
          SELECT status 
          FROM "Group_members" AS gm 
          WHERE gm.group_id = ${filters.group_id}
          AND gm.user_id = "User".id
          AND gm.is_deleted = false
        )`),
        'groupMembershipStatus'
      ],
      [
        Sequelize.literal(`(
          SELECT role 
          FROM "Group_members" AS gm 
          WHERE gm.group_id = ${filters.group_id}
          AND gm.user_id = "User".id
          AND gm.is_deleted = false
        )`),
        'groupRole'
      ]
    ];
  }

  const userDocs = await User.findAndCountAll({
    where: whereClause,
    attributes: [
      "id",
      "username",
      "first_name",
      "last_name",
      "email",
      "profile_picture_url",
      "cover_picture_url",
      "location",
      "country",
      "created_at",
      "privacy_status",
      "gender",
      ...groupMembershipAttributes
    ],
    include: [
      {
        model: Role,
        as: "userRole",
        attributes: ["role", "roleDisplayName"],
      },
    ],
    raw: true,
    limit,
    offset,
  });

  // Get friend statuses and counts for all users using Neo4j
  const users = await Promise.all(userDocs.rows.map(async (userDoc) => {
    // Get friend status using Neo4j
    const myStatus = await Friend.getFriendshipStatus(user.id, userDoc.id);
    const friendStatus = await Friend.getFriendshipStatus(userDoc.id, user.id);
    
    // Get follower count using Neo4j
    const followers = await Friend.getFollowers(userDoc.id);
    const followersCount = followers.length;

    // Get mutual followers using Neo4j
    const mutualFollowers = await Friend.getMutualFollowers(user.id, userDoc.id);
    const mutualFriendsCount = mutualFollowers.length;

    return {
      ...userDoc,
      myStatus: myStatus?.status || null,
      friendStatus: friendStatus?.status || null,
      followersCount,
      mutualFriendsCount
    };
  }));

  return {
    total: userDocs.count,
    users,
    page,
    limit,
  };
};

const editMobileEmail = async ({ user_id, mobile_number, email }) => {
  let user = await User.findOne({ where: { id: user_id } });

  if (!user) throw new ApiError(httpStatus.BAD_REQUEST, "no user found");
  if (
    mobile_number &&
    mobile_number !== "" &&
    user.mobile_number === mobile_number
  )
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "can not edit same mobile number again"
    );
  else if (user.email === email)
    throw new ApiError(httpStatus.BAD_REQUEST, "can not edit same email again");

  const existingUser =
    mobile_number && mobile_number !== ""
      ? await User.findOne({
          where: {
            mobile_number,
            id: {
              [Op.ne]: user_id,
            },
          },
        })
      : await User.findOne({
          where: {
            email,
            id: {
              [Op.ne]: user_id,
            },
          },
        });

  if (existingUser)
    throw new ApiError(httpStatus.BAD_REQUEST, "already user exists");

  const otp = generateOTP();
  if (mobile_number && mobile_number !== "") {
    await sendSms(phone_verification(otp), mobile_number);
    await generatemobile_numberVerificationToken(mobile_number, otp);
    await user.update({ mobile_number, is_mobile_verified: false });
  } else {
    await sendEmail(
      email.toLowerCase(),
      "Verification Mail || Pippan",
      "verification-mail",
      {
        otp,
        token: token.token,
      }
    );
    await generateMailVerificationToken(email, otp);
    await user.update({ email, is_email_verified: false });
  }

  //  user = mobile_number && mobile_number !== "" ?await User.findOne({where:{}})
};

const axios = require("axios");

// const getPlaceDetails = async (place_id) => {
//     const response = await axios.get(
//       'https://maps.googleapis.com/maps/api/place/details/json',
//       {
//         params: {
//           place_id,
//           key: process.env.GOOGLE_MAPS_API_KEY,
//           fields: 'geometry,name,formatted_address', // what you want to fetch
//         },
//       }
//     );

//     return response.data.result;
//   };
const getPlacesSuggestions = async (reqQuery) => {
  const { input } = reqQuery;

  const autocompleteRes = await axios.get(
    "https://maps.googleapis.com/maps/api/place/autocomplete/json",
    {
      params: {
        input,
        key: process.env.GOOGLE_MAPS_API_KEY,
      },
    }
  );

  const predictions = autocompleteRes.data.predictions;

  // Fetch details for each suggestion (optional: do only for top 1)
  const detailedSuggestions = await Promise.all(
    predictions.map(async (place) => {
      const detailsRes = await axios.get(
        "https://maps.googleapis.com/maps/api/place/details/json",
        {
          params: {
            place_id: place.place_id,
            key: process.env.GOOGLE_MAPS_API_KEY,
            fields: "geometry,name,formatted_address",
          },
        }
      );

      const details = detailsRes.data.result;

      return {
        description: place.description,
        place_id: place.place_id,
        lat: details.geometry.location.lat,
        lng: details.geometry.location.lng,
        address: details.formatted_address,
      };
    })
  );

  return detailedSuggestions;
};

const getMutualFollowers = async (userId, currentUserId) => {
  const mutualFollowers = await Friend.getMutualFollowers(userId, currentUserId);
  return mutualFollowers;
};

const addUserFcmToken = async (reqBody,user) => {
  const { fcmToken } = reqBody;
  await User.update({ fcmToken:fcmToken }, { where: { id: user.id } });
  return { message: "FCM token updated successfully", fcmToken:fcmToken };
};

module.exports = {
  addDetails,
  profile,
  getAllUsers,
  editMobileEmail,
  uploadProfilePicture,
  uploadCoverPicture,
  getPlacesSuggestions,
  getMutualFollowers,
  getCountries,
  addUserFcmToken
};
