const { catchAsync } = require("../../../utils/catchAsync");
// const { authService, tokenService } = require("../services");
// const { authService } = require("../../../allservice")
const authService = require("../services/auth.service")
const httpStatus = require("http-status");



const handleRequest = (serviceFunction, reqQuery , reqFile, reqParam, socialLogin) => {
    return catchAsync(async (req, res) => {
      let user = req.user? req.user : {}
      let googleUser =req.user && req.user["_json"] ? req.user["_json"] : ""
      // console.log("request user===================>",)
      const requestField = reqQuery?req.query:reqFile?{file:req.file,body:req.body}:reqParam?req.params:socialLogin?googleUser:req.body
      const result = await serviceFunction(requestField, user);
      socialLogin?res.redirect(process.env.FRONTEND_BASE_URL+`?success=true&token=${result.token.refresh.token}`):res.status(httpStatus.OK).json({success: true,result});
    });
  };

  module.exports.register = handleRequest(authService.register);
  module.exports.login = handleRequest(authService.login);
  module.exports.verifyCredential = handleRequest(authService.verifyCredential);
  module.exports.googleMobile = handleRequest(authService.googleMobile);
  module.exports.facebookMobile = handleRequest(authService.facebookMobile);
  module.exports.forgetPassword = handleRequest(authService.forgetPassword);
  module.exports.sendOtp = handleRequest(authService.sendOtp);
  module.exports.verifyUsername = handleRequest(authService.verifyUsername);
  module.exports.validateOtp = handleRequest(authService.validateOtp);
  module.exports.resetPassword = handleRequest(authService.resetPassword);
  module.exports.googleLogin = handleRequest(authService.googleLogin, false , false , false , true);
  module.exports.facebookLogin = handleRequest(authService.facebookLogin, false , false , false , true);
  module.exports.logout = handleRequest(authService.logout,true);
  module.exports.phoneLogin = handleRequest(authService.phoneLogin);
  module.exports.verifyPhoneLogin = handleRequest(authService.verifyPhoneLogin);
 