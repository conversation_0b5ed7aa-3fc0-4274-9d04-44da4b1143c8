const { catchAsync } = require("../../../utils/catchAsync");
const userService = require("../services/user.service")
const httpStatus = require("http-status");

const {handleRequest} = require("../../../helpers/handleRequest")

module.exports.getCountries = handleRequest(userService.getCountries, true);
module.exports.addDetails = handleRequest(userService.addDetails);
module.exports.profile = handleRequest(userService.profile, true);
module.exports.getAllUsers = handleRequest(userService.getAllUsers);
module.exports.editMobileEmail = handleRequest(userService.editMobileEmail);
module.exports.getPlacesSuggestions = handleRequest(userService.getPlacesSuggestions, true);
module.exports.addUserFcmToken = handleRequest(userService.addUserFcmToken);