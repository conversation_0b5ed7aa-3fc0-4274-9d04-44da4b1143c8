const Joi = require("joi");
const { userRoles, userGenders } = require("../../../configuration/constant");

module.exports.getCountries = {
    query: Joi.object().keys({
        name: Joi.string().optional()
    })
}

module.exports.addDetails = {
    body: Joi.object().keys({
        first_name: Joi.string().optional().allow(null, ""),
        last_name: Joi.string().optional().allow(null, ""),
        date_of_birth: Joi.string().optional().allow(null, ""),
        profile_picture_url: Joi.string().optional().allow(null, ""),
        cover_picture_url: Joi.string().optional().allow(null, ""),
        bio: Joi.string().optional().allow(null, ""),
        gender: Joi.string().valid(...Object.values(userGenders)).optional(),
        is_tc_agreed: Joi.boolean().default(false),
        location: Joi.string().optional().allow(null, ""),
        country: Joi.string().optional().allow(null, ""),
        language: Joi.string().optional().allow(null, ""),
        privacy_status: Joi.string().optional().allow(null , ""),
        locationLongitude: Joi.string().optional().allow(null , ""),
        locationLatitude: Joi.string().optional().allow(null , "")
    }),
};
module.exports.editUser = {
    body: Joi.object().keys({
        name: Joi.string().required()  
    }),
};

module.exports.getAllUsers = {
    body: Joi.object().keys({
        filters: Joi.object().required(),
        page: Joi.number().required(),
        limit: Joi.number().required()
    }),
};
module.exports.editMobileEmail = {
    body: Joi.object().keys({
        user_id: Joi.number().required(),
        email: Joi.string().optional().allow(null, ""),
        mobile_number: Joi.string().optional().allow(null, "")
    }),
};
module.exports.getPlacesSuggestions = {
    query: Joi.object().keys({
        input: Joi.string().required()
    }),
};
module.exports.addUserFcmToken = {
    body: Joi.object().keys({
        fcmToken: Joi.string().required()
    }),
};
