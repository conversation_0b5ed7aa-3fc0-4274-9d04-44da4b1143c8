const Joi = require("joi");
const {
  userRoles,
  approvalStatuses,
} = require("../../../configuration/constant");

module.exports.register = {
  body: Joi.object().keys({
    username: Joi.string().required().messages({
        'any.required': 'Username is required.',
        'string.base': 'Username must be a string.'
      }),
      email: Joi.string().email().required().messages({
        'any.required': 'Email is required.',
        'string.base': 'Email must be a string.',
        'string.email': 'Email must be a valid email address.'
      }),
      password: Joi.string().required().messages({
        'any.required': 'Password is required.',
        'string.base': 'Password must be a string.'
      }),
      language: Joi.string().optional().allow(null, ""),
      country: Joi.string().optional().allow(null, ""),
      location: Joi.string().optional().allow(null, ""),
      first_name: Joi.string().required().messages({
        'any.required': 'First name is required.',
        'string.base': 'First name must be a string.'
      }),
      last_name: Joi.string().required().messages({
        'any.required': 'Last name is required.',
        'string.base': 'Last name must be a string.'
      }),
      date_of_birth: Joi.string().required().messages({
        'any.required': 'Date of birth is required.',
        'string.base': 'Date of birth must be a string.'
      }),
      mobile_number: Joi.string().optional().allow(null, ""),
      gender: Joi.string().required().messages({
        'any.required': 'Gender is required.',
        'string.base': 'Gender must be a string.'
      }),
      role: Joi.number().integer().required().messages({
        'any.required': 'Role is required.',
        'number.base': 'Role must be a number.',
        'number.integer': 'Role must be an integer.'
      }),
      is_tc_agreed: Joi.boolean().required().messages({
        'any.required': 'Terms and Conditions agreement is required.',
        'string.base': 'Terms and Conditions agreement must be boolean.'
      }),
  }),
};

module.exports.login = {
  body: Joi.object().keys({
    email: Joi.string().required(),
    password: Joi.string().required(),
  }),
};

module.exports.phoneLogin = {
  body: Joi.object().keys({
    mobile_number: Joi.string().required().messages({
      'any.required': 'Mobile number is required.',
      'string.base': 'Mobile number must be a string.'
    }),
  }),
};

module.exports.verifyPhoneLogin = {
  body: Joi.object().keys({
    token: Joi.string().required(),
    mobile_number: Joi.string().required().messages({
      'any.required': 'Mobile number is required.',
      'string.base': 'Mobile number must be a string.'
    }),
    otp: Joi.string().required().messages({
      'any.required': 'OTP is required.',
      'string.base': 'OTP must be a string.'
    }),
  }),
};

module.exports.verifyCredential = {
  body: Joi.object().keys({
    email: Joi.string().required(),
    otp: Joi.string().required(),
    verifying: Joi.string().optional().allow(null , "")
  }),
};

module.exports.googleMobile = {
  body: Joi.object().keys({
    idToken: Joi.string().required(),
  }),
};
module.exports.facebookMobile = {
  body: Joi.object().keys({
    accessToken: Joi.string().required(),
  }),
};
module.exports.forgetPassword = {
  body: Joi.object().keys({
    email: Joi.string().required(),
  }),
};
module.exports.sendOtp = {
  body: Joi.object().keys({
    email: Joi.string().optional().allow(null , ""),
    mobile_number: Joi.string().optional().allow(null, ""),
  }),
};
module.exports.validateOtp = {
  body: Joi.object().keys({
    token: Joi.string().required(),
    otp: Joi.string().required(),
  }),
};
module.exports.resetPassword = {
  body: Joi.object().keys({
    token: Joi.string().required(),
    otp: Joi.string().required(),
    password: Joi.string().required(),
  }),
};
