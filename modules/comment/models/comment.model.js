'use strict';

module.exports = (sequelize, DataTypes) => {
  const Comment = sequelize.define('Comment', {
    post_id: {
      type: DataTypes.INTEGER
    },
    user_id:{
      type: DataTypes.INTEGER
    },
    media_url: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    parent_id: {
      type: DataTypes.INTEGER,
      defaultValue: null
    },
    content:{
      type: DataTypes.STRING
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    created_at:{
      type: DataTypes.DATE
    },
    updated_at:{
      type: DataTypes.DATE
    },
  }, {
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });

    Comment.associate = (models) => {
      Comment.belongsTo(models.Post, { foreignKey: 'post_id', as: 'post' });
      Comment.belongsTo(models.User, { foreignKey: 'user_id', as: 'user' });
    };
  return Comment;
};