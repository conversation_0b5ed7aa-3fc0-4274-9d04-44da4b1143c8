const express = require("express");
const router = express.Router();
const validate = require("../../../helpers/validate");
const  commentValidation  = require("../validations/comment.validations");
const commentController = require("../controllers/comment.controller");
const multer = require('multer');
const { storage } = require("../../../configuration/storage");
const { authorize } = require("../../../authwires/auth");
const upload = multer({ storage: storage });


router.use(authorize)

/**
 * @swagger
 * /comment/add-comment:
 *   post:
 *     summary: add comment
 *     tags:
 *       - Comment
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               media_url:
 *                 type: array
 *                 items:
 *                  type: object
 *                  properties:
 *                    media_type:
 *                      type: string
 *                    url:
 *                      type: string
 *               post_id:
 *                 type: number
 *                 description: Display name for the comment
 *               user_id:
 *                 type: number
 *                 description: Comment of the user
 *               content:
 *                 type: string
 *                 description: Comment of the user
 *               parent_id:
 *                 type: number
 *                 description: Comment of the user
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/add-comment', validate(commentValidation.addComment), commentController.addComment)
/**
 * @swagger
 * /comment/edit-comment:
 *   post:
 *     summary: edit comment
 *     tags:
 *       - Comment
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               comment_id:
 *                 type: number
 *               media_url:
 *                 type: array
 *                 items:
 *                  type: object
 *                  properties:
 *                    media_type:
 *                      type: string
 *                    url:
 *                      type: string
 *               user_id:
 *                 type: number
 *                 description: Display name for the comment
 *               post_id:
 *                 type: number
 *                 description: Comment of the user
 *               content:
 *                 type: string
 *                 description: Comment of the user
 *               is_deleted:
 *                 type: boolean
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/edit-comment', validate(commentValidation.editComment), commentController.editComment)



// authorize routes
// router.use(authorize)



/**
 * @swagger
 * /comment/get-comments:
 *   post:
 *     summary: fetch color preferences
 *     tags:
 *       - Comment
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               filters:
 *                 type: object
 *                 description: comment name
 *               page:
 *                type: number
 *               limit:
 *                type: number
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/get-comments', validate(commentValidation.getComments), commentController.getComments)
// router.post('/get-comment', validate(commentValidation.getComment), commentController.getComment)
// router.post('/delete-comment', validate(commentValidation.deleteComment), commentController.deleteComment)


module.exports = router;
