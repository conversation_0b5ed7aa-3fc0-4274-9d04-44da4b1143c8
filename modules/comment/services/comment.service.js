const {Comment, Follower, User, Post} = require("../../../models")
const Friend = require("../../friend/models/friend.model");
const httpStatus = require("http-status");
const ApiError = require("../../../utils/ApiError");
const { Sequelize } = require("sequelize");
const { client: redisClient, set: redisSet, get: redisGet, setHash: redisSetHash, getHash: redisGetHash, getAllHash: redisGetAllHash } = require("../../../services/redis.service");
const logger = require("../../../utils/logger");
// const User = require("../../user/models/user.model");
// const activityService = require("../../activity/services/activity.service");
// const { _isObjectId } = require("../../../helpers/global.functions");
// const  mongoose = require("mongoose");

// Redis key generators
const getPostKey = (postId) => `post:${postId}`;
const getPostsListKey = (userId) => `posts:${userId}`;

// Helper function to update post cache with new comment count
const updatePostCache = async (postId) => {
    try {
        // Get current comment count from database
        const commentCount = await Comment.count({
            where: {
                post_id: postId,
                is_deleted: false
            }
        });

        // Update individual post cache
        const postKey = getPostKey(postId);
        const cachedPost = await redisGet(postKey);
        
        if (cachedPost) {
            cachedPost.commentCount = commentCount;
            await redisSet(postKey, cachedPost);
            logger.info(`Updated post cache for post ${postId} with new comment count: ${commentCount}`);
        }

        // Update post in all users' lists
        const allKeys = await redisClient.keys('posts:*');
        for (const key of allKeys) {
            if (key.includes(':count:')) continue; // Skip count keys
            
            const existingPosts = await redisGetAllHash(key) || {};
            if (existingPosts[postId]) {
                const post = existingPosts[postId];
                post.commentCount = commentCount;
                await redisSetHash(key, postId, post);
                logger.info(`Updated post in list ${key} with new comment count: ${commentCount}`);
            }
        }
    } catch (error) {
        logger.error(`Error updating post cache for post ${postId}:`, error);
    }
};

const addComment = async(reqBody, user) =>{
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }
    reqBody["user_id"] = user.id;
    const comment = await Comment.create(reqBody)

    // Update post cache with new comment count
    if (reqBody.post_id) {
        await updatePostCache(reqBody.post_id);
    }

    if(reqBody.createdBy){
       // await activityService.addActivity({actionOn: reqBody.createdBy, actionBy: reqBody.createdBy, actionName: "addComment" , actionDescription: `comment name ${reqBody.commentDisplayName}` })
    }
    return comment
}
const editComment = async(reqBody) =>{
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }

    // Get the comment to find its post_id before updating
    const comment = await Comment.findOne({
        where: { id: reqBody.comment_id },
        attributes: ['post_id']
    });

    if (!comment) {
        throw new ApiError(httpStatus.NOT_FOUND, "Comment not found");
    }

    const [updatedCount, updatedComments] = await Comment.update(reqBody, {
        where: { id: reqBody.comment_id },
        returning: true,
    });

    if (updatedCount === 0) {
        throw new ApiError(httpStatus.NOT_FOUND, "Comment not found");
    }

    // Update post cache with new comment count
    if (comment.post_id) {
        await updatePostCache(comment.post_id);
    }

    return updatedComments[0];
}

const getComments = async ({ filters, page, limit }, user) => {
    // Clean and transform filters
    Object.entries(filters).forEach(([key, value]) => {
        if (value === "") delete filters[key];
    });

    // Define pagination options
    const offset = (page - 1) * limit;

    if(!filters.parent_id) filters.parent_id = null;
    // Fetch comments using Sequelize
    const commentDocs = await Comment.findAndCountAll({
        include: [
            {
                model: User,
                as: 'user',
                where: { is_deleted: false },
                attributes: ["id", "username", "email", "profile_picture_url", "first_name", "last_name"]
            },   
        ],
        where: filters,
        attributes: [
            "id",
            "user_id", 
            "post_id", 
            "content", 
            "created_at",
            "media_url",
            [
                Sequelize.literal(`(
                    SELECT COUNT(*) 
                    FROM "Comments" AS c
                    WHERE c.parent_id = "Comment"."id"
                    AND c.is_deleted = false
                )`),
                'totalReplies'
            ],
            [
                Sequelize.literal(`(
                    SELECT COUNT(*) 
                    FROM "Likes" AS l
                    WHERE l.comment_id = "Comment"."id"
                    AND l.is_deleted = false
                )`),
                'totalLikes'
            ],
            [Sequelize.literal(`(
                SELECT COUNT(*) > 0 
                FROM "Likes" AS "l" 
                WHERE 
                    "l"."comment_id" = "Comment"."id" AND 
                    "l"."user_id" = :userId AND
                    "l"."is_deleted" = false
            )`), 'isLiked']
        ],
        limit,
        offset,
    });

    // Get friend statuses for all comments using Neo4j
    const commentsWithFriendStatus = await Promise.all(commentDocs.rows.map(async (comment) => {
        const user = comment.user;
        
        // Get friend status using Neo4j
        const myStatus = await Friend.getFriendshipStatus(user.id, user.id);
        const friendStatus = await Friend.getFriendshipStatus(user.id, user.id);
        const followers = await Friend.getFollowers(user.id);
        const followerCount = followers.length;

        return {
            ...comment.toJSON(),
            user: {
                ...user.toJSON(),
                myStatus: myStatus?.status || null,
                friendStatus: friendStatus?.status || null,
                totalFollowers: followerCount
            }
        };
    }));

    return {
        total: commentDocs.count,
        comments: commentsWithFriendStatus,
        page,
        limit
    };
};

module.exports={
    addComment,
    getComments,
    editComment
}