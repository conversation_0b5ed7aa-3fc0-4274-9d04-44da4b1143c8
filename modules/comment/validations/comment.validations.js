const Joi = require("joi");

module.exports.addComment = {
    body: Joi.object().keys({
        post_id: Joi.number().required(),
        content: Joi.string().required(),
        media_url: Joi.array().required(),
        parent_id: Joi.number().optional().allow(null, ""),
    }),
};
module.exports.editComment = {
    body: Joi.object().keys({
        comment_id: Joi.number().required(),
        post_id: Joi.number().optional().allow(null, ""),
        content: Joi.string().optional().allow(null, ""),
        user_id: Joi.number().optional().allow(null , ""),
        is_deleted: Joi.boolean().optional().allow(null, ""),
        media_url: Joi.array().required(),
    }),
};
module.exports.getComments = {
    body: Joi.object().keys({
        filters: Joi.object().required(),
        page: Joi.number().required(),
        limit: Joi.number().required()
    }),
};