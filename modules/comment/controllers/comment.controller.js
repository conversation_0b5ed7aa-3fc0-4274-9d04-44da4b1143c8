const { catchAsync } = require("../../../utils/catchAsync");
// const { commentService, tokenService } = require("../services");
// const { commentService } = require("../../../allservice")
const commentService = require("../services/comment.service")
const httpStatus = require("http-status");



const {handleRequest} = require("../../../helpers/handleRequest")
  module.exports.addComment = handleRequest(commentService.addComment);
  module.exports.editComment = handleRequest(commentService.editComment);
  module.exports.getComments = handleRequest(commentService.getComments);
 