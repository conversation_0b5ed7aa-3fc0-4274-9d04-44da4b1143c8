const { catchAsync } = require("../../../utils/catchAsync");
// const { tagService, tokenService } = require("../services");
// const { tagService } = require("../../../allservice")
const tagService = require("../services/tag.service")
const httpStatus = require("http-status");



const {handleRequest} = require("../../../helpers/handleRequest")
  module.exports.addTag = handleRequest(tagService.addTag);
  module.exports.editTag = handleRequest(tagService.editTag);
  module.exports.getTags = handleRequest(tagService.getTags);
 