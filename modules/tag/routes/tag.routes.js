const express = require("express");
const router = express.Router();
const validate = require("../../../helpers/validate");
const  tagValidation  = require("../validations/tag.validations");
const tagController = require("../controllers/tag.controller");
const multer = require('multer');
const { storage } = require("../../../configuration/storage");
const { authorize } = require("../../../authwires/auth");
const upload = multer({ storage: storage });


router.use(authorize)

/**
 * @swagger
 * /tag/add-tag:
 *   post:
 *     summary: add tag
 *     tags:
 *       - Tag
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               post_id:
 *                 type: number
 *                 description: Display name for the tag
 *               tagged_user_id:
 *                 type: number
 *                 description: Tag of the user
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/add-tag', validate(tagValidation.addTag), tagController.addTag)
/**
 * @swagger
 * /tag/edit-tag:
 *   post:
 *     summary: edit tag
 *     tags:
 *       - Tag
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               tag_id:
 *                 type: number
 *               tagged_user_id:
 *                 type: number
 *                 description: Display name for the tag
 *               post_id:
 *                 type: number
 *                 description: Tag of the user
 *               is_deleted:
 *                 type: boolean
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */

router.post('/edit-tag', validate(tagValidation.editTag), tagController.editTag)



// authorize routes
// router.use(authorize)



/**
 * @swagger
 * /tag/get-tags:
 *   post:
 *     summary: fetch color preferences
 *     tags:
 *       - Tag
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Connection details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               filters:
 *                 type: object
 *                 description: tag name
 *               page:
 *                type: number
 *               limit:
 *                type: number
 *     responses:
 *        200:
 *          description: Connection created successfully.
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */


router.post('/get-tags', validate(tagValidation.getTags), tagController.getTags)
// router.post('/get-tag', validate(tagValidation.getTag), tagController.getTag)
// router.post('/delete-tag', validate(tagValidation.deleteTag), tagController.deleteTag)


module.exports = router;
