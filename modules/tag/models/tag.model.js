'use strict';

module.exports = (sequelize, DataTypes) => {
  const Tag = sequelize.define('Tag', {
    post_id: {
      type: DataTypes.INTEGER
    },
    tagged_user_id:{
      type: DataTypes.INTEGER
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    created_at:{
      type: DataTypes.DATE
    },
    updated_at:{
      type: DataTypes.DATE
    },
  }, {
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });

    Tag.associate = (models) => {
      Tag.belongsTo(models.User, { foreignKey: 'tagged_user_id', as: 'taggedUser' });
      Tag.belongsTo(models.Post, { foreignKey: 'post_id', as: 'post' });
    };
    
  return Tag;
};