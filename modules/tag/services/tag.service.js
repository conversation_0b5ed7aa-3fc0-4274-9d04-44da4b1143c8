const {Tag, Follower, Post, User} = require("../../../models")
const httpStatus = require("http-status");
const ApiError = require("../../../utils/ApiError");
const { client: redisClient, set: redisSet, get: redisGet, setHash: redisSetHash } = require("../../../services/redis.service");
const { Sequelize } = require('sequelize');
// const activityService = require("../../activity/services/activity.service");
// const { _isObjectId } = require("../../../helpers/global.functions");
// const  mongoose = require("mongoose");

// Redis key generators
const getPostKey = (postId) => `post:${postId}`;
const getPostsListKey = (userId) => `posts:${userId}`;

const addTag = async(reqBody) => {
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }
    
    const tag = await Tag.create(reqBody);
    
    // Get the tagged user details
    const taggedUser = await User.findOne({
        where: { id: reqBody.tagged_user_id },
        attributes: ['id', 'username', 'first_name', 'last_name']
    });

    if (taggedUser) {
        try {
            // Get the cached post
            const postKey = getPostKey(reqBody.post_id);
            const cachedPost = await redisGet(postKey);

            if (cachedPost) {
                // Parse the cached post data
                const postData = JSON.parse(cachedPost);
                
                // Initialize tags array if it doesn't exist
                if (!postData.tags) {
                    postData.tags = [];
                }

                // Check if tag already exists
                const tagExists = postData.tags.some(existingTag => 
                    existingTag.tagged_user_id === tag.tagged_user_id
                );

                if (!tagExists) {
                    // Add the new tag to the post's tags array
                    const newTag = {
                        id: tag.id,
                        post_id: tag.post_id,
                        tagged_user_id: tag.tagged_user_id,
                        is_deleted: false,
                        created_at: tag.created_at,
                        updated_at: tag.updated_at,
                        taggedUser: taggedUser.get({ plain: true })
                    };
                    
                    postData.tags.push(newTag);

                    // Update the post in Redis
                    await redisSet(postKey, JSON.stringify(postData));

                    // Update in user's posts list
                    const postsListKey = getPostsListKey(postData.user_id);
                    await redisSetHash(postsListKey, postData.id.toString(), JSON.stringify(postData));
                }
            }
        } catch (error) {
            console.error('Error updating Redis cache:', error);
            // Continue even if Redis update fails
        }
    }

    if(reqBody.createdBy){
        // await activityService.addActivity({actionOn: reqBody.createdBy, actionBy: reqBody.createdBy, actionName: "addTag" , actionDescription: `tag name ${reqBody.tagDisplayName}` })
    }
    
    return tag.get({ plain: true });
}

const editTag = async(reqBody) =>{
    for(let [key, value] of Object.entries(reqBody)){
        if(value === "") delete reqBody[key]
    }
    const [updatedCount, updatedTags] = await Tag.update(reqBody, {
        where: { id: reqBody.tag_id },
        returning: true,
    });

    if (updatedCount === 0) {
        throw new ApiError(httpStatus.NOT_FOUND, "Tag not found");
    }

    // await activityService.addActivity({
    //     actionOn: user?.id,
    //     actionBy: user?.id,
    //     actionName: "addDetails",
    //     actionDescription: "Adding personal details",
    // });

    return updatedTags[0];
}

const getTags = async ({ filters, page, limit }, user) => {
    // console.log(user);

    // Clean and transform filters
    Object.entries(filters).forEach(([key, value]) => {
        if (value === "") delete filters[key];
    });

    // Define pagination options
    const offset = (page - 1) * limit;

    // Fetch tags using Sequelize
    const tagDocs = await Tag.findAndCountAll({
        // include: [
        //     {
        //       model: Follower,
        //       required: true,
        //       attributes: [],
        //       where: {
        //         follower_id: user.id,
        //         is_deleted: false
        //       }
        //     }
        //   ],
        where: filters,
        attributes: ["tagged_user_id", "post_id"],
        limit,
        offset,
    });

    return {
        total: tagDocs.count,
        tags: tagDocs.rows,
        page,
        limit
    };
};

module.exports={
    addTag,
    getTags,
    editTag
}