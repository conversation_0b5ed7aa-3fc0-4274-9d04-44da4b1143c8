const Joi = require("joi");

module.exports.addTag = {
    body: Joi.object().keys({
        post_id: Joi.number().required(),
        tagged_user_id: Joi.number().required()
    }),
};
module.exports.editTag = {
    body: Joi.object().keys({
        tag_id: Joi.number().required(),
        post_id: Joi.number().optional().allow(null, ""),
        tagged_user_id: Joi.number().optional().allow(null , ""),
        is_deleted: Joi.boolean().optional().allow(null, "")
    }),
};
module.exports.getTags = {
    body: Joi.object().keys({
        filters: Joi.object().required(),
        page: Joi.number().required(),
        limit: Joi.number().required()
    }),
};