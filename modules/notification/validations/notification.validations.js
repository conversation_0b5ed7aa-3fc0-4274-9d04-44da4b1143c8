const Joi = require('joi');

const subscribeToTopics = {
  body: Joi.object().keys({
    fcmToken: Joi.string().required(),
    topics: Joi.array().items(Joi.string()).optional()
  })
};

const unsubscribeFromTopic = {
  body: Joi.object().keys({
    fcmToken: Joi.string().required(),
    topic: Joi.string().required()
  })
};

const sendNotificationToTopic = {
  body: Joi.object().keys({
    templateKey: Joi.string().required(),
    templateData: Joi.object().required()
  })
};

const sendNotificationToToken = {
  body: Joi.object().keys({
    templateKey: Joi.string().required(),
    fcmToken: Joi.string().required(),
    templateData: Joi.object().required()
  })
};

const sendFriendRequest = {
  body: Joi.object().keys({
    fullName: Joi.string().required(),
    userFirstName: Joi.string().required()
  })
};

const sendPostLiked = {
  body: Joi.object().keys({
    fullName: Joi.string().required(),
    userFirstName: Joi.string().required()
  })
};

const sendNewComment = {
  body: Joi.object().keys({
    fullName: Joi.string().required(),
    userFirstName: Joi.string().required(),
    commentExcerpt: Joi.string().required()
  })
};

const sendNewMessage = {
  body: Joi.object().keys({
    fullName: Joi.string().required(),
    userFirstName: Joi.string().required()
  })
};

const sendLiveSessionStarted = {
  body: Joi.object().keys({
    fullName: Joi.string().required(),
    userFirstName: Joi.string().required()
  })
};

const sendGroupActivity = {
  body: Joi.object().keys({
    activityType: Joi.string().valid('new_post', 'new_comment', 'announcement').required(),
    groupName: Joi.string().required(),
    fullName: Joi.string().required(),
    userFirstName: Joi.string().optional()
  })
};

const sendFamilyActivity = {
  body: Joi.object().keys({
    activityType: Joi.string().valid('new_member', 'new_post', 'new_comment').required(),
    familyGroupName: Joi.string().required(),
    fullName: Joi.string().required(),
    userFirstName: Joi.string().optional()
  })
};

// Individual Template Validations
const basicUserValidation = {
  body: Joi.object().keys({
    fullName: Joi.string().required(),
    userFirstName: Joi.string().required()
  })
};

const commentValidation = {
  body: Joi.object().keys({
    fullName: Joi.string().required(),
    userFirstName: Joi.string().required(),
    commentExcerpt: Joi.string().required()
  })
};

const milestoneValidation = {
  body: Joi.object().keys({
    userFirstName: Joi.string().required(),
    likeCount: Joi.number().integer().required()
  })
};

const groupValidation = {
  body: Joi.object().keys({
    groupName: Joi.string().required(),
    fullName: Joi.string().required(),
    userFirstName: Joi.string().optional()
  })
};

const groupAnnouncementValidation = {
  body: Joi.object().keys({
    groupName: Joi.string().required(),
    userFirstName: Joi.string().required()
  })
};

const familyValidation = {
  body: Joi.object().keys({
    familyGroupName: Joi.string().required(),
    fullName: Joi.string().required(),
    userFirstName: Joi.string().optional()
  })
};

const familyCommentValidation = {
  body: Joi.object().keys({
    familyGroupName: Joi.string().required(),
    fullName: Joi.string().required()
  })
};

const tokenBasedValidation = {
  body: Joi.object().keys({
    fcmToken: Joi.string().required(),
    userFirstName: Joi.string().required()
  })
};

const tokenWithNameValidation = {
  body: Joi.object().keys({
    fcmToken: Joi.string().required(),
    fullName: Joi.string().required(),
    userFirstName: Joi.string().required()
  })
};

module.exports = {
  subscribeToTopics,
  unsubscribeFromTopic,
  sendNotificationToTopic,
  sendNotificationToToken,
  sendFriendRequest,
  sendPostLiked,
  sendNewComment,
  sendNewMessage,
  sendLiveSessionStarted,
  sendGroupActivity,
  sendFamilyActivity,

  // Individual template validations
  basicUserValidation,
  commentValidation,
  milestoneValidation,
  groupValidation,
  groupAnnouncementValidation,
  familyValidation,
  familyCommentValidation,
  tokenBasedValidation,
  tokenWithNameValidation
};
