const { catchAsync } = require("../../../utils/catchAsync");
const notificationService = require("../services/notification.service");
const httpStatus = require("http-status");

// Subscribe device token to notification topics
const subscribeToTopics = catchAsync(async (req, res) => {
  const { fcmToken, topics } = req.body;
  
  const result = await notificationService.subscribeToMultipleTopics(fcmToken, topics);
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'Successfully subscribed to topics',
    data: result
  });
});

// Unsubscribe device token from a topic
const unsubscribeFromTopic = catchAsync(async (req, res) => {
  const { fcmToken, topic } = req.body;
  
  const result = await notificationService.unsubscribeFromTopic(fcmToken, topic);
  
  res.status(httpStatus.OK).json({
    success: true,
    message: `Successfully unsubscribed from topic: ${topic}`,
    data: result
  });
});

// Send notification to topic using template
const sendNotificationToTopic = catchAsync(async (req, res) => {
  const { templateKey, templateData } = req.body;
  
  const result = await notificationService.sendTemplateNotificationToTopic(
    templateKey,
    templateData
  );
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'Notification sent to topic successfully',
    data: result
  });
});

// Send notification to specific device token
const sendNotificationToToken = catchAsync(async (req, res) => {
  const { templateKey, fcmToken, templateData } = req.body;
  
  const result = await notificationService.sendTemplateNotificationToToken(
    templateKey,
    fcmToken,
    templateData
  );
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'Notification sent to device successfully',
    data: result
  });
});

// Send friend request notification
const sendFriendRequest = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  
  const result = await notificationService.sendFriendRequestNotification({
    fullName,
    userFirstName
  });
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'Friend request notification sent',
    data: result
  });
});

// Send post liked notification
const sendPostLiked = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  
  const result = await notificationService.sendPostLikedNotification({
    fullName,
    userFirstName
  });
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'Post liked notification sent',
    data: result
  });
});

// Send new comment notification
const sendNewComment = catchAsync(async (req, res) => {
  const { fullName, userFirstName, commentExcerpt } = req.body;
  
  const result = await notificationService.sendNewCommentNotification({
    fullName,
    userFirstName,
    commentExcerpt
  });
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'New comment notification sent',
    data: result
  });
});

// Send new message notification
const sendNewMessage = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  
  const result = await notificationService.sendNewMessageNotification({
    fullName,
    userFirstName
  });
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'New message notification sent',
    data: result
  });
});

// Send live session started notification
const sendLiveSessionStarted = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  
  const result = await notificationService.sendLiveSessionStartedNotification({
    fullName,
    userFirstName
  });
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'Live session started notification sent',
    data: result
  });
});

// Send group activity notification
const sendGroupActivity = catchAsync(async (req, res) => {
  const { activityType, groupName, fullName, userFirstName } = req.body;
  
  let result;
  switch (activityType) {
    case 'new_post':
      result = await notificationService.sendGroupNewPostNotification({
        groupName,
        fullName,
        userFirstName
      });
      break;
    case 'new_comment':
      result = await notificationService.sendGroupNewCommentNotification({
        groupName,
        fullName
      });
      break;
    case 'announcement':
      result = await notificationService.sendGroupAnnouncementNotification({
        groupName,
        userFirstName
      });
      break;
    default:
      return res.status(httpStatus.BAD_REQUEST).json({
        success: false,
        message: 'Invalid activity type'
      });
  }
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'Group activity notification sent',
    data: result
  });
});

// Send family activity notification
const sendFamilyActivity = catchAsync(async (req, res) => {
  const { activityType, familyGroupName, fullName, userFirstName } = req.body;
  
  let result;
  switch (activityType) {
    case 'new_member':
      result = await notificationService.sendFamilyNewMemberNotification({
        familyGroupName,
        fullName,
        userFirstName
      });
      break;
    case 'new_post':
      result = await notificationService.sendFamilyNewPostNotification({
        familyGroupName,
        fullName,
        userFirstName
      });
      break;
    case 'new_comment':
      result = await notificationService.sendFamilyNewCommentNotification({
        familyGroupName,
        fullName
      });
      break;
    default:
      return res.status(httpStatus.BAD_REQUEST).json({
        success: false,
        message: 'Invalid family activity type'
      });
  }
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'Family activity notification sent',
    data: result
  });
});

// Get available notification templates
const getNotificationTemplates = catchAsync(async (req, res) => {
  const templates = notificationService.NOTIFICATION_TEMPLATES;

  res.status(httpStatus.OK).json({
    success: true,
    message: 'Notification templates retrieved successfully',
    data: templates
  });
});

// Friend Request Template APIs
const sendFriendRequestReceived = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  const result = await notificationService.sendFriendRequestNotification({ fullName, userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Friend request notification sent', data: result });
});

const sendFriendRequestAccepted = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  const result = await notificationService.sendFriendRequestAcceptedNotification({ fullName, userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Friend request accepted notification sent', data: result });
});

const sendFriendRequestDeclined = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  const result = await notificationService.sendFriendRequestDeclinedNotification({ fullName, userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Friend request declined notification sent', data: result });
});

const sendFriendSuggestion = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  const result = await notificationService.sendFriendSuggestionNotification({ fullName, userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Friend suggestion notification sent', data: result });
});

// Post Interaction Template APIs
const sendPostReacted = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  const result = await notificationService.sendPostReactedNotification({ fullName, userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Post reacted notification sent', data: result });
});

const sendMilestoneUnlocked = catchAsync(async (req, res) => {
  const { userFirstName, likeCount } = req.body;
  const result = await notificationService.sendMilestoneNotification({ userFirstName, likeCount });
  res.status(httpStatus.OK).json({ success: true, message: 'Milestone notification sent', data: result });
});

// Comment Template APIs
const sendCommentReply = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  const result = await notificationService.sendCommentReplyNotification({ fullName, userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Comment reply notification sent', data: result });
});

const sendCommentMention = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  const result = await notificationService.sendCommentMentionNotification({ fullName, userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Comment mention notification sent', data: result });
});

// Tag and Mention Template APIs
const sendPostTag = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  const result = await notificationService.sendTagNotification({ fullName, userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Post tag notification sent', data: result });
});

const sendPostMention = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  const result = await notificationService.sendMentionNotification({ fullName, userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Post mention notification sent', data: result });
});

// Message Template APIs
const sendGroupChatAdded = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  const result = await notificationService.sendGroupChatAddedNotification({ fullName, userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Group chat added notification sent', data: result });
});

// Story Template APIs
const sendStoryReaction = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  const result = await notificationService.sendStoryReactionNotification({ fullName, userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Story reaction notification sent', data: result });
});

const sendStoryReply = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  const result = await notificationService.sendStoryReplyNotification({ fullName, userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Story reply notification sent', data: result });
});

// Group Template APIs
const sendGroupNewPost = catchAsync(async (req, res) => {
  const { groupName, fullName, userFirstName } = req.body;
  const result = await notificationService.sendGroupNewPostNotification({ groupName, fullName, userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Group new post notification sent', data: result });
});

const sendGroupNewComment = catchAsync(async (req, res) => {
  const { groupName, fullName } = req.body;
  const result = await notificationService.sendGroupNewCommentNotification({ groupName, fullName });
  res.status(httpStatus.OK).json({ success: true, message: 'Group new comment notification sent', data: result });
});

const sendGroupAnnouncement = catchAsync(async (req, res) => {
  const { groupName, userFirstName } = req.body;
  const result = await notificationService.sendGroupAnnouncementNotification({ groupName, userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Group announcement notification sent', data: result });
});

// Profile and Security Template APIs
const sendProfileView = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  const result = await notificationService.sendProfileViewNotification({ fullName, userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Profile view notification sent', data: result });
});

const sendPasswordChanged = catchAsync(async (req, res) => {
  const { fcmToken, userFirstName } = req.body;
  const result = await notificationService.sendPasswordChangedNotification(fcmToken, { userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Password changed notification sent', data: result });
});

// Post and Media Template APIs
const sendPostPublished = catchAsync(async (req, res) => {
  const { fcmToken, userFirstName } = req.body;
  const result = await notificationService.sendPostPublishedNotification(fcmToken, { userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Post published notification sent', data: result });
});

const sendPostRemoved = catchAsync(async (req, res) => {
  const { fcmToken, userFirstName } = req.body;
  const result = await notificationService.sendPostRemovedNotification(fcmToken, { userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Post removed notification sent', data: result });
});

const sendPostReported = catchAsync(async (req, res) => {
  const { fcmToken, fullName, userFirstName } = req.body;
  const result = await notificationService.sendPostReportedNotification(fcmToken, { fullName, userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Post reported notification sent', data: result });
});

// Family Group Template APIs
const sendFamilyNewMember = catchAsync(async (req, res) => {
  const { familyGroupName, fullName, userFirstName } = req.body;
  const result = await notificationService.sendFamilyNewMemberNotification({ familyGroupName, fullName, userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Family new member notification sent', data: result });
});

const sendFamilyNewPost = catchAsync(async (req, res) => {
  const { familyGroupName, fullName, userFirstName } = req.body;
  const result = await notificationService.sendFamilyNewPostNotification({ familyGroupName, fullName, userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Family new post notification sent', data: result });
});

const sendFamilyNewComment = catchAsync(async (req, res) => {
  const { familyGroupName, fullName } = req.body;
  const result = await notificationService.sendFamilyNewCommentNotification({ familyGroupName, fullName });
  res.status(httpStatus.OK).json({ success: true, message: 'Family new comment notification sent', data: result });
});

// Live Session Template APIs
const sendLiveSessionEnded = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  const result = await notificationService.sendLiveSessionEndedNotification({ fullName, userFirstName });
  res.status(httpStatus.OK).json({ success: true, message: 'Live session ended notification sent', data: result });
});

module.exports = {
  subscribeToTopics,
  unsubscribeFromTopic,
  sendNotificationToTopic,
  sendNotificationToToken,
  sendFriendRequest,
  sendPostLiked,
  sendNewComment,
  sendNewMessage,
  sendLiveSessionStarted,
  sendGroupActivity,
  sendFamilyActivity,
  getNotificationTemplates,

  // Friend Request Templates
  sendFriendRequestReceived,
  sendFriendRequestAccepted,
  sendFriendRequestDeclined,
  sendFriendSuggestion,

  // Post Interaction Templates
  sendPostReacted,
  sendMilestoneUnlocked,

  // Comment Templates
  sendCommentReply,
  sendCommentMention,

  // Tag and Mention Templates
  sendPostTag,
  sendPostMention,

  // Message Templates
  sendGroupChatAdded,

  // Story Templates
  sendStoryReaction,
  sendStoryReply,

  // Group Templates
  sendGroupNewPost,
  sendGroupNewComment,
  sendGroupAnnouncement,

  // Profile and Security Templates
  sendProfileView,
  sendPasswordChanged,

  // Post and Media Templates
  sendPostPublished,
  sendPostRemoved,
  sendPostReported,

  // Family Group Templates
  sendFamilyNewMember,
  sendFamilyNewPost,
  sendFamilyNewComment,

  // Live Session Templates
  sendLiveSessionEnded
};
