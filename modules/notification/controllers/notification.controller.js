const { catchAsync } = require("../../../utils/catchAsync");
const notificationService = require("../services/notification.service");
const httpStatus = require("http-status");

// Subscribe device token to notification topics
const subscribeToTopics = catchAsync(async (req, res) => {
  const { fcmToken, topics } = req.body;
  
  const result = await notificationService.subscribeToMultipleTopics(fcmToken, topics);
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'Successfully subscribed to topics',
    data: result
  });
});

// Unsubscribe device token from a topic
const unsubscribeFromTopic = catchAsync(async (req, res) => {
  const { fcmToken, topic } = req.body;
  
  const result = await notificationService.unsubscribeFromTopic(fcmToken, topic);
  
  res.status(httpStatus.OK).json({
    success: true,
    message: `Successfully unsubscribed from topic: ${topic}`,
    data: result
  });
});

// Send notification to topic using template
const sendNotificationToTopic = catchAsync(async (req, res) => {
  const { templateKey, templateData } = req.body;
  
  const result = await notificationService.sendTemplateNotificationToTopic(
    templateKey,
    templateData
  );
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'Notification sent to topic successfully',
    data: result
  });
});

// Send notification to specific device token
const sendNotificationToToken = catchAsync(async (req, res) => {
  const { templateKey, fcmToken, templateData } = req.body;
  
  const result = await notificationService.sendTemplateNotificationToToken(
    templateKey,
    fcmToken,
    templateData
  );
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'Notification sent to device successfully',
    data: result
  });
});

// Send friend request notification
const sendFriendRequest = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  
  const result = await notificationService.sendFriendRequestNotification({
    fullName,
    userFirstName
  });
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'Friend request notification sent',
    data: result
  });
});

// Send post liked notification
const sendPostLiked = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  
  const result = await notificationService.sendPostLikedNotification({
    fullName,
    userFirstName
  });
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'Post liked notification sent',
    data: result
  });
});

// Send new comment notification
const sendNewComment = catchAsync(async (req, res) => {
  const { fullName, userFirstName, commentExcerpt } = req.body;
  
  const result = await notificationService.sendNewCommentNotification({
    fullName,
    userFirstName,
    commentExcerpt
  });
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'New comment notification sent',
    data: result
  });
});

// Send new message notification
const sendNewMessage = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  
  const result = await notificationService.sendNewMessageNotification({
    fullName,
    userFirstName
  });
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'New message notification sent',
    data: result
  });
});

// Send live session started notification
const sendLiveSessionStarted = catchAsync(async (req, res) => {
  const { fullName, userFirstName } = req.body;
  
  const result = await notificationService.sendLiveSessionStartedNotification({
    fullName,
    userFirstName
  });
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'Live session started notification sent',
    data: result
  });
});

// Send group activity notification
const sendGroupActivity = catchAsync(async (req, res) => {
  const { activityType, groupName, fullName, userFirstName } = req.body;
  
  let result;
  switch (activityType) {
    case 'new_post':
      result = await notificationService.sendGroupNewPostNotification({
        groupName,
        fullName,
        userFirstName
      });
      break;
    case 'new_comment':
      result = await notificationService.sendGroupNewCommentNotification({
        groupName,
        fullName
      });
      break;
    case 'announcement':
      result = await notificationService.sendGroupAnnouncementNotification({
        groupName,
        userFirstName
      });
      break;
    default:
      return res.status(httpStatus.BAD_REQUEST).json({
        success: false,
        message: 'Invalid activity type'
      });
  }
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'Group activity notification sent',
    data: result
  });
});

// Send family activity notification
const sendFamilyActivity = catchAsync(async (req, res) => {
  const { activityType, familyGroupName, fullName, userFirstName } = req.body;
  
  let result;
  switch (activityType) {
    case 'new_member':
      result = await notificationService.sendFamilyNewMemberNotification({
        familyGroupName,
        fullName,
        userFirstName
      });
      break;
    case 'new_post':
      result = await notificationService.sendFamilyNewPostNotification({
        familyGroupName,
        fullName,
        userFirstName
      });
      break;
    case 'new_comment':
      result = await notificationService.sendFamilyNewCommentNotification({
        familyGroupName,
        fullName
      });
      break;
    default:
      return res.status(httpStatus.BAD_REQUEST).json({
        success: false,
        message: 'Invalid family activity type'
      });
  }
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'Family activity notification sent',
    data: result
  });
});

// Get available notification templates
const getNotificationTemplates = catchAsync(async (req, res) => {
  const templates = notificationService.NOTIFICATION_TEMPLATES;
  
  res.status(httpStatus.OK).json({
    success: true,
    message: 'Notification templates retrieved successfully',
    data: templates
  });
});

module.exports = {
  subscribeToTopics,
  unsubscribeFromTopic,
  sendNotificationToTopic,
  sendNotificationToToken,
  sendFriendRequest,
  sendPostLiked,
  sendNewComment,
  sendNewMessage,
  sendLiveSessionStarted,
  sendGroupActivity,
  sendFamilyActivity,
  getNotificationTemplates
};
