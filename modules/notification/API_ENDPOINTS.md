# Notification Module API Endpoints

Complete list of all available notification API endpoints for Firebase push notifications.

## 🔧 Core Management APIs

### Subscription Management
- `POST /api/notifications/subscribe` - Subscribe device token to topics
- `POST /api/notifications/unsubscribe` - Unsubscribe device token from topic

### Core Notification Sending
- `POST /api/notifications/send-to-topic` - Send notification to topic using template
- `POST /api/notifications/send-to-token` - Send notification to specific device token

## 👥 Friend Request Template APIs

- `POST /api/notifications/friend-request-received` - Send friend request received notification
- `POST /api/notifications/friend-request-accepted` - Send friend request accepted notification
- `POST /api/notifications/friend-request-declined` - Send friend request declined notification
- `POST /api/notifications/friend-suggestion` - Send friend suggestion notification

**Payload Example:**
```json
{
  "fullName": "John Doe",
  "userFirstName": "Jane"
}
```

## 👍 Post Interaction Template APIs

- `POST /api/notifications/post-liked` - Send post liked notification
- `POST /api/notifications/post-reacted` - Send post reacted notification
- `POST /api/notifications/milestone-unlocked` - Send milestone unlocked notification

**Payload Examples:**
```json
// For likes/reactions
{
  "fullName": "John Doe",
  "userFirstName": "Jane"
}

// For milestones
{
  "userFirstName": "Jane",
  "likeCount": 100
}
```

## 💬 Comment Template APIs

- `POST /api/notifications/new-comment` - Send new comment notification
- `POST /api/notifications/comment-reply` - Send comment reply notification
- `POST /api/notifications/comment-mention` - Send comment mention notification

**Payload Examples:**
```json
// For new comments
{
  "fullName": "John Doe",
  "userFirstName": "Jane",
  "commentExcerpt": "Great post! I really enjoyed reading this..."
}

// For replies/mentions
{
  "fullName": "John Doe",
  "userFirstName": "Jane"
}
```

## 🏷️ Tag and Mention Template APIs

- `POST /api/notifications/post-tag` - Send post tag notification
- `POST /api/notifications/post-mention` - Send post mention notification

**Payload Example:**
```json
{
  "fullName": "John Doe",
  "userFirstName": "Jane"
}
```

## 💌 Message Template APIs

- `POST /api/notifications/new-message` - Send new message notification
- `POST /api/notifications/group-chat-added` - Send group chat added notification

**Payload Example:**
```json
{
  "fullName": "John Doe",
  "userFirstName": "Jane"
}
```

## 📖 Story Template APIs

- `POST /api/notifications/story-reaction` - Send story reaction notification
- `POST /api/notifications/story-reply` - Send story reply notification

**Payload Example:**
```json
{
  "fullName": "John Doe",
  "userFirstName": "Jane"
}
```

## 👥 Group Template APIs

- `POST /api/notifications/group-new-post` - Send group new post notification
- `POST /api/notifications/group-new-comment` - Send group new comment notification
- `POST /api/notifications/group-announcement` - Send group announcement notification

**Payload Examples:**
```json
// For posts/comments
{
  "groupName": "Photography Enthusiasts",
  "fullName": "John Doe",
  "userFirstName": "Jane"
}

// For announcements
{
  "groupName": "Photography Enthusiasts",
  "userFirstName": "Jane"
}
```

## 👁️ Profile and Security Template APIs

- `POST /api/notifications/profile-view` - Send profile view notification
- `POST /api/notifications/password-changed` - Send password changed notification

**Payload Examples:**
```json
// For profile views
{
  "fullName": "John Doe",
  "userFirstName": "Jane"
}

// For password changes (sent to specific device)
{
  "fcmToken": "device-fcm-token",
  "userFirstName": "Jane"
}
```

## 📝 Post and Media Template APIs

- `POST /api/notifications/post-published` - Send post published notification
- `POST /api/notifications/post-removed` - Send post removed notification
- `POST /api/notifications/post-reported` - Send post reported notification

**Payload Examples:**
```json
// For published/removed posts
{
  "fcmToken": "device-fcm-token",
  "userFirstName": "Jane"
}

// For reported posts
{
  "fcmToken": "device-fcm-token",
  "fullName": "John Doe",
  "userFirstName": "Jane"
}
```

## 👨‍👩‍👧‍👦 Family Group Template APIs

- `POST /api/notifications/family-new-member` - Send family new member notification
- `POST /api/notifications/family-new-post` - Send family new post notification
- `POST /api/notifications/family-new-comment` - Send family new comment notification

**Payload Examples:**
```json
// For members/posts
{
  "familyGroupName": "Smith Family",
  "fullName": "John Doe",
  "userFirstName": "Jane"
}

// For comments
{
  "familyGroupName": "Smith Family",
  "fullName": "John Doe"
}
```

## 🔴 Live Session Template APIs

- `POST /api/notifications/live-session-started` - Send live session started notification
- `POST /api/notifications/live-session-ended` - Send live session ended notification

**Payload Example:**
```json
{
  "fullName": "John Doe",
  "userFirstName": "Jane"
}
```

## 🔧 Legacy/Grouped APIs (for backward compatibility)

- `POST /api/notifications/friend-request` - Send friend request notification
- `POST /api/notifications/group-activity` - Send group activity notification
- `POST /api/notifications/family-activity` - Send family activity notification

## 📋 Utility APIs

- `GET /api/notifications/templates` - Get available notification templates

## 📊 API Response Format

All APIs return a consistent response format:

```json
{
  "success": true,
  "message": "Notification sent successfully",
  "data": {
    "messageId": "firebase-message-id"
  }
}
```

## 🔍 Testing

You can test any endpoint using curl:

```bash
curl -X POST http://localhost:8080/api/notifications/friend-request-received \
  -H "Content-Type: application/json" \
  -d '{
    "fullName": "John Doe",
    "userFirstName": "Jane"
  }'
```

## 📖 Swagger Documentation

All endpoints are documented in Swagger UI at:
- **Swagger UI**: `http://localhost:8080/apidoc`
- **Swagger JSON**: `http://localhost:8080/apidoc-json`

Look for the "Notification Templates" tag in Swagger for all individual template endpoints.

## 🎯 Topics Used

Each template sends to a specific Firebase topic:
- `friend_requests`, `friend_updates`, `friend_suggestions`
- `post_interactions`, `milestones`
- `mentions`
- `messages`, `group_chats`
- `story_interactions`
- `group_activities`, `group_announcements`
- `family_activities`
- `live_sessions`
- `profile_activities`, `security_alerts`
- `post_updates`, `moderation_alerts`

Frontend applications can subscribe to these topics to receive notifications in real-time.
