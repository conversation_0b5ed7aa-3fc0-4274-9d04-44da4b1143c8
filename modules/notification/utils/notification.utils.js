const notificationService = require('../services/notification.service');

/**
 * Notification utility functions for easy integration across the application
 * All functions send notifications to Firebase topics for frontend consumption
 */

// Friend-related notifications
const notifyFriendRequest = async (senderData, recipientData) => {
  try {
    return await notificationService.sendFriendRequestNotification({
      fullName: senderData.fullName,
      userFirstName: recipientData.firstName
    });
  } catch (error) {
    console.error('Error sending friend request notification:', error);
  }
};

const notifyFriendRequestAccepted = async (accepterData, requesterData) => {
  try {
    return await notificationService.sendFriendRequestAcceptedNotification({
      fullName: accepterData.fullName,
      userFirstName: requesterData.firstName
    });
  } catch (error) {
    console.error('Error sending friend request accepted notification:', error);
  }
};

// Post interaction notifications
const notifyPostLiked = async (likerData, postOwnerData) => {
  try {
    return await notificationService.sendPostLikedNotification({
      fullName: likerData.fullName,
      userFirstName: postOwnerData.firstName
    });
  } catch (error) {
    console.error('Error sending post liked notification:', error);
  }
};

const notifyPostCommented = async (commenterData, postOwnerData, commentText) => {
  try {
    return await notificationService.sendNewCommentNotification({
      fullName: commenterData.fullName,
      userFirstName: postOwnerData.firstName,
      commentExcerpt: commentText.length > 50 ? commentText.substring(0, 50) + '...' : commentText
    });
  } catch (error) {
    console.error('Error sending post comment notification:', error);
  }
};

const notifyPostTagged = async (taggerData, taggedUserData) => {
  try {
    return await notificationService.sendTagNotification({
      fullName: taggerData.fullName,
      userFirstName: taggedUserData.firstName
    });
  } catch (error) {
    console.error('Error sending post tag notification:', error);
  }
};

// Message notifications
const notifyNewMessage = async (senderData, recipientData) => {
  try {
    return await notificationService.sendNewMessageNotification({
      fullName: senderData.fullName,
      userFirstName: recipientData.firstName
    });
  } catch (error) {
    console.error('Error sending new message notification:', error);
  }
};

// Group notifications
const notifyGroupActivity = async (groupName, actorData, activityType, userData = null) => {
  try {
    switch (activityType) {
      case 'new_post':
        return await notificationService.sendGroupNewPostNotification({
          groupName,
          fullName: actorData.fullName,
          userFirstName: userData?.firstName || ''
        });
      case 'new_comment':
        return await notificationService.sendGroupNewCommentNotification({
          groupName,
          fullName: actorData.fullName
        });
      case 'announcement':
        return await notificationService.sendGroupAnnouncementNotification({
          groupName,
          userFirstName: userData?.firstName || ''
        });
      default:
        console.warn(`Unknown group activity type: ${activityType}`);
    }
  } catch (error) {
    console.error('Error sending group activity notification:', error);
  }
};

// Family group notifications
const notifyFamilyActivity = async (familyGroupName, actorData, activityType, userData = null) => {
  try {
    switch (activityType) {
      case 'new_member':
        return await notificationService.sendFamilyNewMemberNotification({
          familyGroupName,
          fullName: actorData.fullName,
          userFirstName: userData?.firstName || ''
        });
      case 'new_post':
        return await notificationService.sendFamilyNewPostNotification({
          familyGroupName,
          fullName: actorData.fullName,
          userFirstName: userData?.firstName || ''
        });
      case 'new_comment':
        return await notificationService.sendFamilyNewCommentNotification({
          familyGroupName,
          fullName: actorData.fullName
        });
      default:
        console.warn(`Unknown family activity type: ${activityType}`);
    }
  } catch (error) {
    console.error('Error sending family activity notification:', error);
  }
};

// Story notifications
const notifyStoryInteraction = async (storyOwnerData, interactorData, interactionType) => {
  try {
    switch (interactionType) {
      case 'reaction':
        return await notificationService.sendStoryReactionNotification({
          fullName: interactorData.fullName,
          userFirstName: storyOwnerData.firstName
        });
      case 'reply':
        return await notificationService.sendStoryReplyNotification({
          fullName: interactorData.fullName,
          userFirstName: storyOwnerData.firstName
        });
      default:
        console.warn(`Unknown story interaction type: ${interactionType}`);
    }
  } catch (error) {
    console.error('Error sending story interaction notification:', error);
  }
};

// Live session notifications
const notifyLiveSession = async (streamerData, sessionType, userData = null) => {
  try {
    switch (sessionType) {
      case 'started':
        return await notificationService.sendLiveSessionStartedNotification({
          fullName: streamerData.fullName,
          userFirstName: userData?.firstName || ''
        });
      case 'ended':
        return await notificationService.sendLiveSessionEndedNotification({
          fullName: streamerData.fullName,
          userFirstName: userData?.firstName || ''
        });
      default:
        console.warn(`Unknown live session type: ${sessionType}`);
    }
  } catch (error) {
    console.error('Error sending live session notification:', error);
  }
};

// Milestone notifications
const notifyMilestone = async (userData, milestoneType, milestoneData) => {
  try {
    switch (milestoneType) {
      case 'likes_milestone':
        return await notificationService.sendMilestoneNotification({
          userFirstName: userData.firstName,
          likeCount: milestoneData.likeCount
        });
      default:
        console.warn(`Unknown milestone type: ${milestoneType}`);
    }
  } catch (error) {
    console.error('Error sending milestone notification:', error);
  }
};

// Subscribe device to default topics
const subscribeDeviceToTopics = async (fcmToken, topics = []) => {
  try {
    return await notificationService.subscribeToMultipleTopics(fcmToken, topics);
  } catch (error) {
    console.error('Error subscribing device to topics:', error);
  }
};

module.exports = {
  // Friend notifications
  notifyFriendRequest,
  notifyFriendRequestAccepted,
  
  // Post interaction notifications
  notifyPostLiked,
  notifyPostCommented,
  notifyPostTagged,
  
  // Message notifications
  notifyNewMessage,
  
  // Group notifications
  notifyGroupActivity,
  notifyFamilyActivity,
  
  // Story notifications
  notifyStoryInteraction,
  
  // Live session notifications
  notifyLiveSession,
  
  // Milestone notifications
  notifyMilestone,
  
  // Utility functions
  subscribeDeviceToTopics
};
