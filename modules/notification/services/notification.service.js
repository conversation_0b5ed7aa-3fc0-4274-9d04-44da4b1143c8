const { admin } = require('../../../configuration/firebase-config');
const { User } = require('../../../models');
const config = require('../../../configuration/config');

// Initialize Firebase Admin SDK
const serviceAccount = require('../../../configuration/serviceAccountKey.json');

const { firebaseApp } = require('../../../configuration/firebase-config');
// const { admin } = require('../../../configuration/firebase-config');

const sendTagNotification = async (taggedUserId, taggerName) => {
  try {
    const user = await User.findOne({
      where: { id: taggedUserId },
      attributes: ['fcmToken']
    });

    if (!user || !user.fcmToken) {
      console.log(`No FCM token found for user ${taggedUserId}`);
      return;
    }

    const message = {
      notification: {
        title: 'New Tag',
        body: `${taggerName} tagged you in a post`
      },
      token: user.fcmToken
    };

    await admin.messaging().send(message);
    console.log('Notification sent successfully');
  } catch (error) {
    console.error('Error sending notification:', error);
  }
};

module.exports = {
  sendTagNotification
};