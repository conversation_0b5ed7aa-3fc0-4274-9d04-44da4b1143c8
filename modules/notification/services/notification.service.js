const { admin } = require('../../../configuration/firebase-config');

// Notification templates for different modules
const NOTIFICATION_TEMPLATES = {
  // Friend Request Module
  FRIEND_REQUEST_RECEIVED: {
    subject: '👋 New Friend Request from {fullName}',
    body: 'Hi {userFirstName},\nYou\'ve received a new friend request from {fullName}.\n[Accept or View Request →]',
    topic: 'friend_requests'
  },
  FRIEND_REQUEST_ACCEPTED: {
    subject: '✅ {fullName} Accepted Your Friend Request',
    body: 'Great news, {userFirstName}!\n{fullName} has accepted your friend request. You\'re now connected!\n[Say Hello →]',
    topic: 'friend_updates'
  },
  FRIEND_REQUEST_DECLINED: {
    subject: '🚫 Friend Request Not Accepted',
    body: 'Hey {userFirstName},\nYour friend request to {fullName} was not accepted. Don\'t worry—keep making new connections!\n[Find New Friends →]',
    topic: 'friend_updates'
  },
  FRIEND_SUGGESTION: {
    subject: '👥 Suggested Friend: {fullName}',
    body: 'Hi {userFirstName},\nWe think you might know {fullName}. Want to send them a friend request?\n[Add as Friend →]',
    topic: 'friend_suggestions'
  },

  // Likes & Reactions Module
  POST_LIKED: {
    subject: '{fullName} Liked Your Post',
    body: 'Hey {userFirstName},\n{fullName} just liked your post. Keep sharing your awesome content!\n[View Post →]',
    topic: 'post_interactions'
  },
  POST_REACTED: {
    subject: '{fullName} Reacted to Your Post',
    body: 'Hi {userFirstName},\n{fullName} reacted to your post. Curious about which reaction they picked?\n[See Reaction →]',
    topic: 'post_interactions'
  },
  MILESTONE_UNLOCKED: {
    subject: '🎉 Milestone Unlocked!',
    body: 'Congrats {userFirstName},\nYour post just hit {likeCount} likes! Keep engaging with your friends and fans.\n[Celebrate with a New Post →]',
    topic: 'milestones'
  },

  // Comments Module
  NEW_COMMENT: {
    subject: '💬 New Comment on Your Post',
    body: 'Hi {userFirstName},\n{fullName} commented: "{commentExcerpt}"\n[Reply or React →]',
    topic: 'post_interactions'
  },
  COMMENT_REPLY: {
    subject: '↩️ {fullName} Replied to Your Comment',
    body: 'Hey {userFirstName},\nYour comment sparked a reply from {fullName}. Check it out!\n[View Reply →]',
    topic: 'post_interactions'
  },
  COMMENT_MENTION: {
    subject: '📣 You Were Mentioned in a Comment',
    body: 'Hi {userFirstName},\n{fullName} mentioned you in a comment.\n[See What They Said →]',
    topic: 'mentions'
  },

  // Tags & Mentions Module
  POST_TAG: {
    subject: '🏷️ You Were Tagged in a Post',
    body: 'Hi {userFirstName},\n{fullName} tagged you in a new post.\n[View Tag →]',
    topic: 'mentions'
  },
  POST_MENTION: {
    subject: '📢 Mentioned in a Post',
    body: 'Hey {userFirstName},\nYou were mentioned in {fullName}\'s recent post.\n[See Post →]',
    topic: 'mentions'
  },

  // Messages Module
  NEW_MESSAGE: {
    subject: '✉️ New Message from {fullName}',
    body: 'Hi {userFirstName},\nYou\'ve got a new message from {fullName} waiting for you.\n[Read Message →]',
    topic: 'messages'
  },
  GROUP_CHAT_ADDED: {
    subject: '👥 Added to a Group Chat',
    body: 'Hey {userFirstName},\n{fullName} added you to a new group chat. Join in on the conversation!\n[Open Chat →]',
    topic: 'group_chats'
  },

  // Stories Module
  STORY_REACTION: {
    subject: '❤️ Reaction to Your Story',
    body: 'Hi {userFirstName},\n{fullName} just reacted to your story.\n[View Reaction →]',
    topic: 'story_interactions'
  },
  STORY_REPLY: {
    subject: '💬 Someone Replied to Your Story',
    body: 'Hey {userFirstName},\n{fullName} replied to your story.\n[Read Reply →]',
    topic: 'story_interactions'
  },

  // Groups Module
  GROUP_NEW_POST: {
    subject: '🧑‍🤝‍🧑 New Post in {groupName}',
    body: 'Hi {userFirstName},\n{fullName} shared a new post in {groupName}.\n[Join the Discussion →]',
    topic: 'group_activities'
  },
  GROUP_NEW_COMMENT: {
    subject: '💬 New Comment in {groupName}',
    body: '{fullName} commented in the group {groupName}.\n[View Comment →]',
    topic: 'group_activities'
  },
  GROUP_ANNOUNCEMENT: {
    subject: '📢 New Group Announcement',
    body: 'Hi {userFirstName},\nThere\'s a new announcement in {groupName}.\n[Read Announcement →]',
    topic: 'group_announcements'
  },

  // Profile & Security Module
  PROFILE_VIEW: {
    subject: '👁️ Someone Viewed Your Profile',
    body: 'Hey {userFirstName},\n{fullName} checked out your profile.\n[View Profile Insights →]',
    topic: 'profile_activities'
  },
  PASSWORD_CHANGED: {
    subject: '🔐 Password Changed Successfully',
    body: 'Hi {userFirstName},\nYour password has been changed successfully. If this wasn\'t you, [Reset Your Password Immediately →]',
    topic: 'security_alerts'
  },

  // Posts & Media Module
  POST_PUBLISHED: {
    subject: '🔁 Your Post Has Been Published',
    body: 'Hi {userFirstName},\nYour new post is live!\n[View Post →]',
    topic: 'post_updates'
  },
  POST_REMOVED: {
    subject: '🚨 Post Removed Due to Policy Violation',
    body: 'Hi {userFirstName},\nYour post was removed because it violated our community guidelines.\n[Review Guidelines →] | [Appeal Decision →]',
    topic: 'moderation_alerts'
  },
  POST_REPORTED: {
    subject: '⚠️ Your Post Was Reported',
    body: 'Hey {userFirstName},\n{fullName} reported your post. Our team will review it shortly.\n[View Reported Post →]',
    topic: 'moderation_alerts'
  },

  // Family Groups Module
  FAMILY_NEW_MEMBER: {
    subject: '👨‍👩‍👧‍👦 New Member in {familyGroupName}',
    body: 'Hi {userFirstName},\n{fullName} joined the family group {familyGroupName}.\n[Say Welcome →]',
    topic: 'family_activities'
  },
  FAMILY_NEW_POST: {
    subject: '🏠 New Post in {familyGroupName}',
    body: 'Hey {userFirstName},\n{fullName} shared a new post in your family group.\n[View Post →]',
    topic: 'family_activities'
  },
  FAMILY_NEW_COMMENT: {
    subject: '💬 New Comment in {familyGroupName}',
    body: '{fullName} left a comment in {familyGroupName}.\n[See Comment →]',
    topic: 'family_activities'
  },

  // Live Sessions Module
  LIVE_SESSION_STARTED: {
    subject: '🔴 {fullName} is Now Live!',
    body: 'Hey {userFirstName},\n{fullName} just went live. Tap to join the stream!\n[Watch Live →]',
    topic: 'live_sessions'
  },
  LIVE_SESSION_ENDED: {
    subject: '⏹️ {fullName} Just Ended Their Live Session',
    body: 'Hi {userFirstName},\n{fullName} has ended their live session. Stay tuned for the next one!\n[View Highlights →]',
    topic: 'live_sessions'
  }
};

// Helper function to replace placeholders in templates
const replacePlaceholders = (template, data) => {
  let subject = template.subject;
  let body = template.body;

  Object.keys(data).forEach(key => {
    const placeholder = `{${key}}`;
    subject = subject.replace(new RegExp(placeholder, 'g'), data[key] || '');
    body = body.replace(new RegExp(placeholder, 'g'), data[key] || '');
  });

  return { subject, body, topic: template.topic };
};

// Subscribe device token to a topic
const subscribeToTopic = async (fcmToken, topic) => {
  try {
    const response = await admin.messaging().subscribeToTopic(fcmToken, topic);
    console.log(`Successfully subscribed to topic ${topic}:`, response);
    return response;
  } catch (error) {
    console.error(`Error subscribing to topic ${topic}:`, error);
    throw error;
  }
};

// Unsubscribe device token from a topic
const unsubscribeFromTopic = async (fcmToken, topic) => {
  try {
    const response = await admin.messaging().unsubscribeFromTopic(fcmToken, topic);
    console.log(`Successfully unsubscribed from topic ${topic}:`, response);
    return response;
  } catch (error) {
    console.error(`Error unsubscribing from topic ${topic}:`, error);
    throw error;
  }
};

// Subscribe device to multiple topics
const subscribeToMultipleTopics = async (fcmToken, topics = []) => {
  try {
    const defaultTopics = [
      'friend_requests',
      'friend_updates',
      'post_interactions',
      'messages',
      'group_chats',
      'story_interactions',
      'group_activities',
      'family_activities',
      'live_sessions'
    ];

    const topicsToSubscribe = topics.length > 0 ? topics : defaultTopics;
    const results = [];

    for (const topic of topicsToSubscribe) {
      try {
        const result = await subscribeToTopic(fcmToken, topic);
        results.push({ topic, success: true, result });
      } catch (error) {
        results.push({ topic, success: false, error: error.message });
      }
    }

    return results;
  } catch (error) {
    console.error('Error subscribing to multiple topics:', error);
    throw error;
  }
};

// Send notification to topic
const sendToTopic = async (topic, title, body, data = {}) => {
  try {
    const message = {
      topic,
      notification: {
        title,
        body
      },
      data: {
        ...data,
        timestamp: new Date().toISOString()
      },
      android: {
        priority: 'high',
        notification: {
          sound: 'default',
          clickAction: 'FLUTTER_NOTIFICATION_CLICK'
        }
      },
      apns: {
        headers: {
          'apns-priority': '10'
        },
        payload: {
          aps: {
            sound: 'default',
            badge: 1
          }
        }
      }
    };

    const response = await admin.messaging().send(message);
    console.log(`Successfully sent message to topic ${topic}:`, response);
    return response;
  } catch (error) {
    console.error(`Error sending message to topic ${topic}:`, error);
    throw error;
  }
};

// Send notification to specific device token
const sendToToken = async (fcmToken, title, body, data = {}) => {
  try {
    const message = {
      token: fcmToken,
      notification: {
        title,
        body
      },
      data: {
        ...data,
        timestamp: new Date().toISOString()
      },
      android: {
        priority: 'high',
        notification: {
          sound: 'default',
          clickAction: 'FLUTTER_NOTIFICATION_CLICK'
        }
      },
      apns: {
        headers: {
          'apns-priority': '10'
        },
        payload: {
          aps: {
            sound: 'default',
            badge: 1
          }
        }
      }
    };

    const response = await admin.messaging().send(message);
    console.log(`Successfully sent message to token:`, response);
    return response;
  } catch (error) {
    console.error(`Error sending message to token:`, error);
    throw error;
  }
};

// Send notification using template to topic
const sendTemplateNotificationToTopic = async (templateKey, templateData = {}) => {
  try {
    const template = NOTIFICATION_TEMPLATES[templateKey];
    if (!template) {
      throw new Error(`Template ${templateKey} not found`);
    }

    const notification = replacePlaceholders(template, templateData);

    return await sendToTopic(
      notification.topic,
      notification.subject,
      notification.body,
      { templateKey, ...templateData }
    );
  } catch (error) {
    console.error('Error sending template notification to topic:', error);
    throw error;
  }
};

// Send notification using template to specific token
const sendTemplateNotificationToToken = async (templateKey, fcmToken, templateData = {}) => {
  try {
    const template = NOTIFICATION_TEMPLATES[templateKey];
    if (!template) {
      throw new Error(`Template ${templateKey} not found`);
    }

    const notification = replacePlaceholders(template, templateData);

    return await sendToToken(
      fcmToken,
      notification.subject,
      notification.body,
      { templateKey, ...templateData }
    );
  } catch (error) {
    console.error('Error sending template notification to token:', error);
    throw error;
  }
};

// Specific notification functions for easy use

// Friend Request Notifications
const sendFriendRequestNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('FRIEND_REQUEST_RECEIVED', templateData);
};

const sendFriendRequestAcceptedNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('FRIEND_REQUEST_ACCEPTED', templateData);
};

const sendFriendRequestDeclinedNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('FRIEND_REQUEST_DECLINED', templateData);
};

const sendFriendSuggestionNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('FRIEND_SUGGESTION', templateData);
};

// Post Interaction Notifications
const sendPostLikedNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('POST_LIKED', templateData);
};

const sendPostReactedNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('POST_REACTED', templateData);
};

const sendMilestoneNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('MILESTONE_UNLOCKED', templateData);
};

// Comment Notifications
const sendNewCommentNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('NEW_COMMENT', templateData);
};

const sendCommentReplyNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('COMMENT_REPLY', templateData);
};

const sendCommentMentionNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('COMMENT_MENTION', templateData);
};

// Tag and Mention Notifications
const sendTagNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('POST_TAG', templateData);
};

const sendMentionNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('POST_MENTION', templateData);
};

// Message Notifications
const sendNewMessageNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('NEW_MESSAGE', templateData);
};

const sendGroupChatAddedNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('GROUP_CHAT_ADDED', templateData);
};

// Story Notifications
const sendStoryReactionNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('STORY_REACTION', templateData);
};

const sendStoryReplyNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('STORY_REPLY', templateData);
};

// Group Notifications
const sendGroupNewPostNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('GROUP_NEW_POST', templateData);
};

const sendGroupNewCommentNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('GROUP_NEW_COMMENT', templateData);
};

const sendGroupAnnouncementNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('GROUP_ANNOUNCEMENT', templateData);
};

// Family Group Notifications
const sendFamilyNewMemberNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('FAMILY_NEW_MEMBER', templateData);
};

const sendFamilyNewPostNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('FAMILY_NEW_POST', templateData);
};

const sendFamilyNewCommentNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('FAMILY_NEW_COMMENT', templateData);
};

// Live Session Notifications
const sendLiveSessionStartedNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('LIVE_SESSION_STARTED', templateData);
};

const sendLiveSessionEndedNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('LIVE_SESSION_ENDED', templateData);
};

// Profile and Security Notifications
const sendProfileViewNotification = async (templateData) => {
  return await sendTemplateNotificationToTopic('PROFILE_VIEW', templateData);
};

const sendPasswordChangedNotification = async (fcmToken, templateData) => {
  return await sendTemplateNotificationToToken('PASSWORD_CHANGED', fcmToken, templateData);
};

// Post and Media Notifications
const sendPostPublishedNotification = async (fcmToken, templateData) => {
  return await sendTemplateNotificationToToken('POST_PUBLISHED', fcmToken, templateData);
};

const sendPostRemovedNotification = async (fcmToken, templateData) => {
  return await sendTemplateNotificationToToken('POST_REMOVED', fcmToken, templateData);
};

const sendPostReportedNotification = async (fcmToken, templateData) => {
  return await sendTemplateNotificationToToken('POST_REPORTED', fcmToken, templateData);
};

module.exports = {
  // Core functions
  subscribeToTopic,
  unsubscribeFromTopic,
  subscribeToMultipleTopics,
  sendToTopic,
  sendToToken,
  sendTemplateNotificationToTopic,
  sendTemplateNotificationToToken,

  // Friend Request notifications
  sendFriendRequestNotification,
  sendFriendRequestAcceptedNotification,
  sendFriendRequestDeclinedNotification,
  sendFriendSuggestionNotification,

  // Post Interaction notifications
  sendPostLikedNotification,
  sendPostReactedNotification,
  sendMilestoneNotification,

  // Comment notifications
  sendNewCommentNotification,
  sendCommentReplyNotification,
  sendCommentMentionNotification,

  // Tag and Mention notifications
  sendTagNotification,
  sendMentionNotification,

  // Message notifications
  sendNewMessageNotification,
  sendGroupChatAddedNotification,

  // Story notifications
  sendStoryReactionNotification,
  sendStoryReplyNotification,

  // Group notifications
  sendGroupNewPostNotification,
  sendGroupNewCommentNotification,
  sendGroupAnnouncementNotification,

  // Family Group notifications
  sendFamilyNewMemberNotification,
  sendFamilyNewPostNotification,
  sendFamilyNewCommentNotification,

  // Live Session notifications
  sendLiveSessionStartedNotification,
  sendLiveSessionEndedNotification,

  // Profile and Security notifications
  sendProfileViewNotification,
  sendPasswordChangedNotification,

  // Post and Media notifications
  sendPostPublishedNotification,
  sendPostRemovedNotification,
  sendPostReportedNotification,

  // Templates (for reference)
  NOTIFICATION_TEMPLATES
};