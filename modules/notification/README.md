# Notification Module

A simple Firebase Cloud Messaging (FCM) notification system with topic-based subscriptions for the Pipaan social media application.

## Features

- **Firebase Cloud Messaging Integration**: Uses existing firebase-config.js for reliable push notifications
- **Topic-Based Subscriptions**: Devices can subscribe to different notification topics
- **Template System**: Pre-defined notification templates for consistent messaging
- **No Database Dependencies**: Pure Firebase push notifications without local storage
- **Frontend Integration**: Notifications are consumed by frontend through Firebase topics

## Notification Templates

The module includes templates for the following categories:

### Friend Requests
- New friend request received
- Friend request accepted
- Friend request declined
- Friend suggestions

### Post Interactions
- Post liked
- Post reacted
- Milestone achievements (like count)

### Comments
- New comment on post
- Comment reply
- Comment mentions

### Tags & Mentions
- Tagged in post
- Mentioned in post

### Messages
- New direct message
- Added to group chat

### Stories
- Story reaction
- Story reply

### Groups
- New post in group
- New comment in group
- Group announcements

### Family Groups
- New family member
- New post in family group
- New comment in family group

### Live Sessions
- Live session started
- Live session ended

### Profile & Security
- Profile viewed
- Password changed

### Posts & Media
- Post published
- Post removed
- Post reported

## API Endpoints

### Subscription Management
- `POST /api/notifications/subscribe` - Subscribe device token to topics
- `POST /api/notifications/unsubscribe` - Unsubscribe device token from topic

### Notification Sending
- `POST /api/notifications/send-to-topic` - Send notification to topic using template
- `POST /api/notifications/send-to-token` - Send notification to specific device token
- `POST /api/notifications/friend-request` - Send friend request notification
- `POST /api/notifications/post-liked` - Send post liked notification
- `POST /api/notifications/new-comment` - Send new comment notification
- `POST /api/notifications/new-message` - Send new message notification
- `POST /api/notifications/live-session-started` - Send live session notification
- `POST /api/notifications/group-activity` - Send group activity notification
- `POST /api/notifications/family-activity` - Send family activity notification

### Utility
- `GET /api/notifications/templates` - Get available notification templates

## Usage Examples

### Subscribe Device to Topics
```javascript
const notificationService = require('./modules/notification/services/notification.service');

// Subscribe device to default topics
await notificationService.subscribeToMultipleTopics(fcmToken);

// Subscribe to specific topics
await notificationService.subscribeToMultipleTopics(fcmToken, ['friend_requests', 'messages']);
```

### Send Friend Request Notification
```javascript
const { notifyFriendRequest } = require('./modules/notification/utils/notification.utils');

await notifyFriendRequest(
  { fullName: 'John Doe' },
  { firstName: 'Jane' }
);
```

### Send Post Interaction Notification
```javascript
const { notifyPostLiked } = require('./modules/notification/utils/notification.utils');

await notifyPostLiked(
  { fullName: 'Jane Smith' },
  { firstName: 'John' }
);
```

### Send Notification to Topic
```javascript
await notificationService.sendTemplateNotificationToTopic('FRIEND_REQUEST_RECEIVED', {
  fullName: 'John Doe',
  userFirstName: 'Jane'
});
```

### Send Notification to Specific Device
```javascript
await notificationService.sendTemplateNotificationToToken(
  'PASSWORD_CHANGED',
  fcmToken,
  { userFirstName: 'John' }
);
```

## Integration with Other Modules

### Friend Module Integration
```javascript
// In friend service when sending friend request
const { notifyFriendRequest } = require('../notification/utils/notification.utils');

const sendFriendRequest = async (senderId, recipientId) => {
  // ... friend request logic
  
  // Send notification to topic
  await notifyFriendRequest(
    { fullName: sender.firstName + ' ' + sender.lastName },
    { firstName: recipient.firstName }
  );
};
```

### Post Module Integration
```javascript
// In post service when post is liked
const { notifyPostLiked } = require('../notification/utils/notification.utils');

const likePost = async (postId, userId) => {
  // ... like logic
  
  // Send notification to topic
  await notifyPostLiked(
    { fullName: user.firstName + ' ' + user.lastName },
    { firstName: postOwner.firstName }
  );
};
```

## Configuration

### Environment Variables
Make sure these are set in your `.env` file:
```
FIREBASE_DATABASE_URL=your_firebase_database_url
```

### Firebase Service Account
Ensure `configuration/serviceAccountKey.json` contains your Firebase service account credentials.

## Topics

The following topics are available for subscription:

- `friend_requests` - Friend request notifications
- `friend_updates` - Friend status updates
- `post_interactions` - Likes, reactions, comments
- `messages` - Direct messages
- `group_chats` - Group chat activities
- `story_interactions` - Story likes and replies
- `group_activities` - Group posts and comments
- `family_activities` - Family group activities
- `live_sessions` - Live streaming notifications
- `milestones` - Achievement notifications
- `mentions` - Tags and mentions
- `security_alerts` - Security-related notifications
- `moderation_alerts` - Content moderation alerts

## Frontend Integration

The frontend can subscribe to these topics using Firebase SDK and receive notifications in real-time:

```javascript
// Frontend Firebase setup
import { messaging } from './firebase-config';
import { getToken, onMessage } from 'firebase/messaging';

// Get FCM token
const token = await getToken(messaging);

// Subscribe to topics (call your backend API)
await fetch('/api/notifications/subscribe', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ fcmToken: token })
});

// Listen for messages
onMessage(messaging, (payload) => {
  console.log('Message received:', payload);
  // Handle notification in your app
});
```

## Testing

Use the API endpoints to test notification delivery:

```bash
# Subscribe device to topics
curl -X POST http://localhost:8080/api/notifications/subscribe \
  -H "Content-Type: application/json" \
  -d '{
    "fcmToken": "your-fcm-token"
  }'

# Send test notification
curl -X POST http://localhost:8080/api/notifications/friend-request \
  -H "Content-Type: application/json" \
  -d '{
    "fullName": "Test User",
    "userFirstName": "John"
  }'
```

## Best Practices

1. **Subscribe devices on app startup** to ensure they receive notifications
2. **Use topic subscriptions** for broad notifications (announcements, live sessions)
3. **Use direct device notifications** for personal notifications (password changes)
4. **Handle notification permissions** properly in your frontend
5. **Test thoroughly** on both Android and iOS devices

## Troubleshooting

### Common Issues

1. **FCM Token Not Valid**: Ensure the token is fresh and valid
2. **Firebase Credentials**: Verify service account key is valid and has proper permissions
3. **Topic Subscription Failures**: Check topic names and Firebase project settings
4. **Notification Not Received**: Verify device is online and app has notification permissions

### Debugging

Enable detailed logging by checking the console output in your Node.js application.
