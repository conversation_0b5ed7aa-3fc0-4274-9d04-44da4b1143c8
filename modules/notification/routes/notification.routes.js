const express = require("express");
const router = express.Router();
const validate = require("../../../helpers/validate");
const notificationValidation = require("../validations/notification.validations");
const notificationController = require("../controllers/notification.controller");

/**
 * @swagger
 * /notification/subscribe:
 *   post:
 *     summary: Subscribe device token to notification topics
 *     tags:
 *       - Notifications
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Subscription details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - fcmToken
 *             properties:
 *               fcmToken:
 *                 type: string
 *                 description: Firebase Cloud Messaging token
 *               topics:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of topics to subscribe to (optional, defaults to all topics)
 *     responses:
 *        200:
 *          description: Successfully subscribed to topics
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */
router.post(
  "/subscribe",
  validate(notificationValidation.subscribeToTopics),
  notificationController.subscribeToTopics
);

/**
 * @swagger
 * /notification/unsubscribe:
 *   post:
 *     summary: Unsubscribe device token from a notification topic
 *     tags:
 *       - Notifications
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Unsubscription details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - fcmToken
 *               - topic
 *             properties:
 *               fcmToken:
 *                 type: string
 *                 description: Firebase Cloud Messaging token
 *               topic:
 *                 type: string
 *                 description: Topic to unsubscribe from
 *     responses:
 *        200:
 *          description: Successfully unsubscribed from topic
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */
router.post(
  "/unsubscribe",
  validate(notificationValidation.unsubscribeFromTopic),
  notificationController.unsubscribeFromTopic
);

/**
 * @swagger
 * /notification/send-to-topic:
 *   post:
 *     summary: Send notification to topic using template
 *     tags:
 *       - Notifications
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Template notification details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - templateKey
 *               - templateData
 *             properties:
 *               templateKey:
 *                 type: string
 *                 description: Template key to use
 *               templateData:
 *                 type: object
 *                 description: Data to populate template
 *     responses:
 *        200:
 *          description: Notification sent to topic successfully
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */
router.post(
  "/send-to-topic",
  validate(notificationValidation.sendNotificationToTopic),
  notificationController.sendNotificationToTopic
);

/**
 * @swagger
 * /notification/send-to-token:
 *   post:
 *     summary: Send notification to specific device token
 *     tags:
 *       - Notifications
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Device notification details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - templateKey
 *               - fcmToken
 *               - templateData
 *             properties:
 *               templateKey:
 *                 type: string
 *                 description: Template key to use
 *               fcmToken:
 *                 type: string
 *                 description: Firebase Cloud Messaging token
 *               templateData:
 *                 type: object
 *                 description: Data to populate template
 *     responses:
 *        200:
 *          description: Notification sent to device successfully
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */
router.post(
  "/send-to-token",
  validate(notificationValidation.sendNotificationToToken),
  notificationController.sendNotificationToToken
);

/**
 * @swagger
 * /notification/friend-request:
 *   post:
 *     summary: Send friend request notification
 *     tags:
 *       - Notifications
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Friend request notification details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - fullName
 *               - userFirstName
 *             properties:
 *               fullName:
 *                 type: string
 *                 description: Full name of the person sending friend request
 *               userFirstName:
 *                 type: string
 *                 description: First name of the recipient
 *     responses:
 *        200:
 *          description: Friend request notification sent successfully
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */
router.post(
  "/friend-request",
  validate(notificationValidation.sendFriendRequest),
  notificationController.sendFriendRequest
);

/**
 * @swagger
 * /notification/post-liked:
 *   post:
 *     summary: Send post liked notification
 *     tags:
 *       - Notifications
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Post liked notification details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - fullName
 *               - userFirstName
 *             properties:
 *               fullName:
 *                 type: string
 *                 description: Full name of the person who liked the post
 *               userFirstName:
 *                 type: string
 *                 description: First name of the post owner
 *     responses:
 *        200:
 *          description: Post liked notification sent successfully
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */
router.post(
  "/post-liked",
  validate(notificationValidation.sendPostLiked),
  notificationController.sendPostLiked
);

/**
 * @swagger
 * /notification/new-comment:
 *   post:
 *     summary: Send new comment notification
 *     tags:
 *       - Notifications
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: New comment notification details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - fullName
 *               - userFirstName
 *               - commentExcerpt
 *             properties:
 *               fullName:
 *                 type: string
 *                 description: Full name of the person who commented
 *               userFirstName:
 *                 type: string
 *                 description: First name of the post owner
 *               commentExcerpt:
 *                 type: string
 *                 description: Excerpt of the comment text
 *     responses:
 *        200:
 *          description: New comment notification sent successfully
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */
router.post(
  "/new-comment",
  validate(notificationValidation.sendNewComment),
  notificationController.sendNewComment
);

/**
 * @swagger
 * /api/notifications/new-message:
 *   post:
 *     summary: Send new message notification
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fullName
 *               - userFirstName
 *             properties:
 *               fullName:
 *                 type: string
 *               userFirstName:
 *                 type: string
 *     responses:
 *       200:
 *         description: New message notification sent
 */
router.post(
  "/new-message",
  validate(notificationValidation.sendNewMessage),
  notificationController.sendNewMessage
);

/**
 * @swagger
 * /api/notifications/live-session-started:
 *   post:
 *     summary: Send live session started notification
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fullName
 *               - userFirstName
 *             properties:
 *               fullName:
 *                 type: string
 *               userFirstName:
 *                 type: string
 *     responses:
 *       200:
 *         description: Live session started notification sent
 */
router.post(
  "/live-session-started",
  validate(notificationValidation.sendLiveSessionStarted),
  notificationController.sendLiveSessionStarted
);

/**
 * @swagger
 * /api/notifications/group-activity:
 *   post:
 *     summary: Send group activity notification
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - activityType
 *               - groupName
 *               - fullName
 *             properties:
 *               activityType:
 *                 type: string
 *                 enum: [new_post, new_comment, announcement]
 *               groupName:
 *                 type: string
 *               fullName:
 *                 type: string
 *               userFirstName:
 *                 type: string
 *     responses:
 *       200:
 *         description: Group activity notification sent
 */
router.post(
  "/group-activity",
  validate(notificationValidation.sendGroupActivity),
  notificationController.sendGroupActivity
);

/**
 * @swagger
 * /api/notifications/family-activity:
 *   post:
 *     summary: Send family activity notification
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - activityType
 *               - familyGroupName
 *               - fullName
 *             properties:
 *               activityType:
 *                 type: string
 *                 enum: [new_member, new_post, new_comment]
 *               familyGroupName:
 *                 type: string
 *               fullName:
 *                 type: string
 *               userFirstName:
 *                 type: string
 *     responses:
 *       200:
 *         description: Family activity notification sent
 */
router.post(
  "/family-activity",
  validate(notificationValidation.sendFamilyActivity),
  notificationController.sendFamilyActivity
);

/**
 * @swagger
 * /notification/templates:
 *   get:
 *     summary: Get available notification templates
 *     tags:
 *       - Notifications
 *     produces:
 *       - application/json
 *     responses:
 *        200:
 *          description: Notification templates retrieved successfully
 *        500:
 *          description: Server Error
 */
router.get("/templates", notificationController.getNotificationTemplates);

// Friend Request Template Routes
/**
 * @swagger
 * /notification/friend-request-received:
 *   post:
 *     summary: Send friend request received notification
 *     tags:
 *       - Notification Templates
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Friend request received notification details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - fullName
 *               - userFirstName
 *             properties:
 *               fullName:
 *                 type: string
 *                 description: Full name of the person sending friend request
 *               userFirstName:
 *                 type: string
 *                 description: First name of the recipient
 *     responses:
 *        200:
 *          description: Friend request received notification sent successfully
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */
router.post("/friend-request-received", validate(notificationValidation.basicUserValidation), notificationController.sendFriendRequestReceived);

/**
 * @swagger
 * /notification/friend-request-accepted:
 *   post:
 *     summary: Send friend request accepted notification
 *     tags:
 *       - Notification Templates
 *     produces:
 *       - application/json
 *     parameters:
 *         - name: body
 *           in: body
 *           description: Friend request accepted notification details
 *           required: true
 *           schema:
 *             type: object
 *             required:
 *               - fullName
 *               - userFirstName
 *             properties:
 *               fullName:
 *                 type: string
 *                 description: Full name of the person who accepted the request
 *               userFirstName:
 *                 type: string
 *                 description: First name of the original requester
 *     responses:
 *        200:
 *          description: Friend request accepted notification sent successfully
 *        400:
 *          description: Bad Request
 *        500:
 *          description: Server Error
 */
router.post("/friend-request-accepted", validate(notificationValidation.basicUserValidation), notificationController.sendFriendRequestAccepted);

/**
 * @swagger
 * /api/notifications/friend-request-declined:
 *   post:
 *     summary: Send friend request declined notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [fullName, userFirstName]
 *             properties:
 *               fullName: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Friend request declined notification sent
 */
router.post("/friend-request-declined", validate(notificationValidation.basicUserValidation), notificationController.sendFriendRequestDeclined);

/**
 * @swagger
 * /api/notifications/friend-suggestion:
 *   post:
 *     summary: Send friend suggestion notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [fullName, userFirstName]
 *             properties:
 *               fullName: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Friend suggestion notification sent
 */
router.post("/friend-suggestion", validate(notificationValidation.basicUserValidation), notificationController.sendFriendSuggestion);

// Post Interaction Template Routes
/**
 * @swagger
 * /api/notifications/post-reacted:
 *   post:
 *     summary: Send post reacted notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [fullName, userFirstName]
 *             properties:
 *               fullName: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Post reacted notification sent
 */
router.post("/post-reacted", validate(notificationValidation.basicUserValidation), notificationController.sendPostReacted);

/**
 * @swagger
 * /api/notifications/milestone-unlocked:
 *   post:
 *     summary: Send milestone unlocked notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [userFirstName, likeCount]
 *             properties:
 *               userFirstName: { type: string }
 *               likeCount: { type: integer }
 *     responses:
 *       200:
 *         description: Milestone unlocked notification sent
 */
router.post("/milestone-unlocked", validate(notificationValidation.milestoneValidation), notificationController.sendMilestoneUnlocked);

// Comment Template Routes
/**
 * @swagger
 * /api/notifications/comment-reply:
 *   post:
 *     summary: Send comment reply notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [fullName, userFirstName]
 *             properties:
 *               fullName: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Comment reply notification sent
 */
router.post("/comment-reply", validate(notificationValidation.basicUserValidation), notificationController.sendCommentReply);

/**
 * @swagger
 * /api/notifications/comment-mention:
 *   post:
 *     summary: Send comment mention notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [fullName, userFirstName]
 *             properties:
 *               fullName: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Comment mention notification sent
 */
router.post("/comment-mention", validate(notificationValidation.basicUserValidation), notificationController.sendCommentMention);

// Tag and Mention Template Routes
/**
 * @swagger
 * /api/notifications/post-tag:
 *   post:
 *     summary: Send post tag notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [fullName, userFirstName]
 *             properties:
 *               fullName: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Post tag notification sent
 */
router.post("/post-tag", validate(notificationValidation.basicUserValidation), notificationController.sendPostTag);

/**
 * @swagger
 * /api/notifications/post-mention:
 *   post:
 *     summary: Send post mention notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [fullName, userFirstName]
 *             properties:
 *               fullName: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Post mention notification sent
 */
router.post("/post-mention", validate(notificationValidation.basicUserValidation), notificationController.sendPostMention);

// Message Template Routes
/**
 * @swagger
 * /api/notifications/group-chat-added:
 *   post:
 *     summary: Send group chat added notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [fullName, userFirstName]
 *             properties:
 *               fullName: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Group chat added notification sent
 */
router.post("/group-chat-added", validate(notificationValidation.basicUserValidation), notificationController.sendGroupChatAdded);

// Story Template Routes
/**
 * @swagger
 * /api/notifications/story-reaction:
 *   post:
 *     summary: Send story reaction notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [fullName, userFirstName]
 *             properties:
 *               fullName: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Story reaction notification sent
 */
router.post("/story-reaction", validate(notificationValidation.basicUserValidation), notificationController.sendStoryReaction);

/**
 * @swagger
 * /api/notifications/story-reply:
 *   post:
 *     summary: Send story reply notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [fullName, userFirstName]
 *             properties:
 *               fullName: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Story reply notification sent
 */
router.post("/story-reply", validate(notificationValidation.basicUserValidation), notificationController.sendStoryReply);

// Group Template Routes
/**
 * @swagger
 * /api/notifications/group-new-post:
 *   post:
 *     summary: Send group new post notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [groupName, fullName]
 *             properties:
 *               groupName: { type: string }
 *               fullName: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Group new post notification sent
 */
router.post("/group-new-post", validate(notificationValidation.groupValidation), notificationController.sendGroupNewPost);

/**
 * @swagger
 * /api/notifications/group-new-comment:
 *   post:
 *     summary: Send group new comment notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [groupName, fullName]
 *             properties:
 *               groupName: { type: string }
 *               fullName: { type: string }
 *     responses:
 *       200:
 *         description: Group new comment notification sent
 */
router.post("/group-new-comment", validate(notificationValidation.groupValidation), notificationController.sendGroupNewComment);

/**
 * @swagger
 * /api/notifications/group-announcement:
 *   post:
 *     summary: Send group announcement notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [groupName, userFirstName]
 *             properties:
 *               groupName: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Group announcement notification sent
 */
router.post("/group-announcement", validate(notificationValidation.groupAnnouncementValidation), notificationController.sendGroupAnnouncement);

// Profile and Security Template Routes
/**
 * @swagger
 * /api/notifications/profile-view:
 *   post:
 *     summary: Send profile view notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [fullName, userFirstName]
 *             properties:
 *               fullName: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Profile view notification sent
 */
router.post("/profile-view", validate(notificationValidation.basicUserValidation), notificationController.sendProfileView);

/**
 * @swagger
 * /api/notifications/password-changed:
 *   post:
 *     summary: Send password changed notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [fcmToken, userFirstName]
 *             properties:
 *               fcmToken: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Password changed notification sent
 */
router.post("/password-changed", validate(notificationValidation.tokenBasedValidation), notificationController.sendPasswordChanged);

// Post and Media Template Routes
/**
 * @swagger
 * /api/notifications/post-published:
 *   post:
 *     summary: Send post published notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [fcmToken, userFirstName]
 *             properties:
 *               fcmToken: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Post published notification sent
 */
router.post("/post-published", validate(notificationValidation.tokenBasedValidation), notificationController.sendPostPublished);

/**
 * @swagger
 * /api/notifications/post-removed:
 *   post:
 *     summary: Send post removed notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [fcmToken, userFirstName]
 *             properties:
 *               fcmToken: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Post removed notification sent
 */
router.post("/post-removed", validate(notificationValidation.tokenBasedValidation), notificationController.sendPostRemoved);

/**
 * @swagger
 * /api/notifications/post-reported:
 *   post:
 *     summary: Send post reported notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [fcmToken, fullName, userFirstName]
 *             properties:
 *               fcmToken: { type: string }
 *               fullName: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Post reported notification sent
 */
router.post("/post-reported", validate(notificationValidation.tokenWithNameValidation), notificationController.sendPostReported);

// Family Group Template Routes
/**
 * @swagger
 * /api/notifications/family-new-member:
 *   post:
 *     summary: Send family new member notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [familyGroupName, fullName]
 *             properties:
 *               familyGroupName: { type: string }
 *               fullName: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Family new member notification sent
 */
router.post("/family-new-member", validate(notificationValidation.familyValidation), notificationController.sendFamilyNewMember);

/**
 * @swagger
 * /api/notifications/family-new-post:
 *   post:
 *     summary: Send family new post notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [familyGroupName, fullName]
 *             properties:
 *               familyGroupName: { type: string }
 *               fullName: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Family new post notification sent
 */
router.post("/family-new-post", validate(notificationValidation.familyValidation), notificationController.sendFamilyNewPost);

/**
 * @swagger
 * /api/notifications/family-new-comment:
 *   post:
 *     summary: Send family new comment notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [familyGroupName, fullName]
 *             properties:
 *               familyGroupName: { type: string }
 *               fullName: { type: string }
 *     responses:
 *       200:
 *         description: Family new comment notification sent
 */
router.post("/family-new-comment", validate(notificationValidation.familyCommentValidation), notificationController.sendFamilyNewComment);

// Live Session Template Routes
/**
 * @swagger
 * /api/notifications/live-session-ended:
 *   post:
 *     summary: Send live session ended notification
 *     tags: [Notification Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [fullName, userFirstName]
 *             properties:
 *               fullName: { type: string }
 *               userFirstName: { type: string }
 *     responses:
 *       200:
 *         description: Live session ended notification sent
 */
router.post("/live-session-ended", validate(notificationValidation.basicUserValidation), notificationController.sendLiveSessionEnded);

module.exports = router;
