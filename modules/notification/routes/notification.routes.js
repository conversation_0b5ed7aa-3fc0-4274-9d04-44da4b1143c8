const express = require("express");
const router = express.Router();
const validate = require("../../../helpers/validate");
const notificationValidation = require("../validations/notification.validations");
const notificationController = require("../controllers/notification.controller");

/**
 * @swagger
 * /api/notifications/subscribe:
 *   post:
 *     summary: Subscribe device token to notification topics
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fcmToken
 *             properties:
 *               fcmToken:
 *                 type: string
 *                 description: Firebase Cloud Messaging token
 *               topics:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of topics to subscribe to (optional, defaults to all topics)
 *     responses:
 *       200:
 *         description: Successfully subscribed to topics
 *       400:
 *         description: Bad request
 */
router.post(
  "/subscribe",
  validate(notificationValidation.subscribeToTopics),
  notificationController.subscribeToTopics
);

/**
 * @swagger
 * /api/notifications/unsubscribe:
 *   post:
 *     summary: Unsubscribe device token from a notification topic
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fcmToken
 *               - topic
 *             properties:
 *               fcmToken:
 *                 type: string
 *                 description: Firebase Cloud Messaging token
 *               topic:
 *                 type: string
 *                 description: Topic to unsubscribe from
 *     responses:
 *       200:
 *         description: Successfully unsubscribed from topic
 *       400:
 *         description: Bad request
 */
router.post(
  "/unsubscribe",
  validate(notificationValidation.unsubscribeFromTopic),
  notificationController.unsubscribeFromTopic
);

/**
 * @swagger
 * /api/notifications/send-to-topic:
 *   post:
 *     summary: Send notification to topic using template
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - templateKey
 *               - templateData
 *             properties:
 *               templateKey:
 *                 type: string
 *                 description: Template key to use
 *               templateData:
 *                 type: object
 *                 description: Data to populate template
 *     responses:
 *       200:
 *         description: Notification sent to topic successfully
 *       400:
 *         description: Bad request
 */
router.post(
  "/send-to-topic",
  validate(notificationValidation.sendNotificationToTopic),
  notificationController.sendNotificationToTopic
);

/**
 * @swagger
 * /api/notifications/send-to-token:
 *   post:
 *     summary: Send notification to specific device token
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - templateKey
 *               - fcmToken
 *               - templateData
 *             properties:
 *               templateKey:
 *                 type: string
 *                 description: Template key to use
 *               fcmToken:
 *                 type: string
 *                 description: Firebase Cloud Messaging token
 *               templateData:
 *                 type: object
 *                 description: Data to populate template
 *     responses:
 *       200:
 *         description: Notification sent to device successfully
 *       400:
 *         description: Bad request
 */
router.post(
  "/send-to-token",
  validate(notificationValidation.sendNotificationToToken),
  notificationController.sendNotificationToToken
);

/**
 * @swagger
 * /api/notifications/friend-request:
 *   post:
 *     summary: Send friend request notification
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fullName
 *               - userFirstName
 *             properties:
 *               fullName:
 *                 type: string
 *               userFirstName:
 *                 type: string
 *     responses:
 *       200:
 *         description: Friend request notification sent
 */
router.post(
  "/friend-request",
  validate(notificationValidation.sendFriendRequest),
  notificationController.sendFriendRequest
);

/**
 * @swagger
 * /api/notifications/post-liked:
 *   post:
 *     summary: Send post liked notification
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fullName
 *               - userFirstName
 *             properties:
 *               fullName:
 *                 type: string
 *               userFirstName:
 *                 type: string
 *     responses:
 *       200:
 *         description: Post liked notification sent
 */
router.post(
  "/post-liked",
  validate(notificationValidation.sendPostLiked),
  notificationController.sendPostLiked
);

/**
 * @swagger
 * /api/notifications/new-comment:
 *   post:
 *     summary: Send new comment notification
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fullName
 *               - userFirstName
 *               - commentExcerpt
 *             properties:
 *               fullName:
 *                 type: string
 *               userFirstName:
 *                 type: string
 *               commentExcerpt:
 *                 type: string
 *     responses:
 *       200:
 *         description: New comment notification sent
 */
router.post(
  "/new-comment",
  validate(notificationValidation.sendNewComment),
  notificationController.sendNewComment
);

/**
 * @swagger
 * /api/notifications/new-message:
 *   post:
 *     summary: Send new message notification
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fullName
 *               - userFirstName
 *             properties:
 *               fullName:
 *                 type: string
 *               userFirstName:
 *                 type: string
 *     responses:
 *       200:
 *         description: New message notification sent
 */
router.post(
  "/new-message",
  validate(notificationValidation.sendNewMessage),
  notificationController.sendNewMessage
);

/**
 * @swagger
 * /api/notifications/live-session-started:
 *   post:
 *     summary: Send live session started notification
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fullName
 *               - userFirstName
 *             properties:
 *               fullName:
 *                 type: string
 *               userFirstName:
 *                 type: string
 *     responses:
 *       200:
 *         description: Live session started notification sent
 */
router.post(
  "/live-session-started",
  validate(notificationValidation.sendLiveSessionStarted),
  notificationController.sendLiveSessionStarted
);

/**
 * @swagger
 * /api/notifications/group-activity:
 *   post:
 *     summary: Send group activity notification
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - activityType
 *               - groupName
 *               - fullName
 *             properties:
 *               activityType:
 *                 type: string
 *                 enum: [new_post, new_comment, announcement]
 *               groupName:
 *                 type: string
 *               fullName:
 *                 type: string
 *               userFirstName:
 *                 type: string
 *     responses:
 *       200:
 *         description: Group activity notification sent
 */
router.post(
  "/group-activity",
  validate(notificationValidation.sendGroupActivity),
  notificationController.sendGroupActivity
);

/**
 * @swagger
 * /api/notifications/family-activity:
 *   post:
 *     summary: Send family activity notification
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - activityType
 *               - familyGroupName
 *               - fullName
 *             properties:
 *               activityType:
 *                 type: string
 *                 enum: [new_member, new_post, new_comment]
 *               familyGroupName:
 *                 type: string
 *               fullName:
 *                 type: string
 *               userFirstName:
 *                 type: string
 *     responses:
 *       200:
 *         description: Family activity notification sent
 */
router.post(
  "/family-activity",
  validate(notificationValidation.sendFamilyActivity),
  notificationController.sendFamilyActivity
);

/**
 * @swagger
 * /api/notifications/templates:
 *   get:
 *     summary: Get available notification templates
 *     tags: [Notifications]
 *     responses:
 *       200:
 *         description: Notification templates retrieved successfully
 */
router.get("/templates", notificationController.getNotificationTemplates);

module.exports = router;
