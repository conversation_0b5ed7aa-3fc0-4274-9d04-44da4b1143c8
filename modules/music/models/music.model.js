'use strict';

module.exports = (sequelize, DataTypes) => {
    const Music = sequelize.define('Music', {
        title: {
            type: DataTypes.STRING,
            allowNull: false
        },
        artist: {
            type: DataTypes.STRING,
            allowNull: false
        },
        duration: {
            type: DataTypes.INTEGER, // Duration in seconds
            allowNull: false
        },
        file_url: {
            type: DataTypes.STRING,
            allowNull: false
        },
        thumbnail_url: {
            type: DataTypes.STRING,
            allowNull: true
        },
        is_deleted: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        },
        created_at: {
            type: DataTypes.DATE
        },
        updated_at: {
            type: DataTypes.DATE
        }
    }, {
        timestamps: true,
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      });

    // Define associations
    Music.associate = (models) => {
        Music.hasMany(models.Story, {
            foreignKey: 'music_id',
            as: 'stories'
        });
    };

    return Music;
}; 