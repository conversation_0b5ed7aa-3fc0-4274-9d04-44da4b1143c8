const Joi = require('joi');

const searchSongs = {
    query: Joi.object().keys({
        query: Joi.string().optional().allow(null, ""),
        page: Joi.number().integer().min(1).default(1),
        limit: Joi.number().integer().min(1).max(100).default(20)
    })
};

const getTrendingSongs = {
    query: Joi.object().keys({
        page: Joi.number().integer().min(1).default(1),
        limit: Joi.number().integer().min(1).max(100).default(20)
    })
};

const getSongDetails = {
    params: Joi.object().keys({
        id: Joi.string().required()
    })
};

module.exports = {
    searchSongs,
    getTrendingSongs,
    getSongDetails
}; 