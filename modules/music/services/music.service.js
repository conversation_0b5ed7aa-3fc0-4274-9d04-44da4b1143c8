const axios = require('axios');
const httpStatus = require('http-status');
const ApiError = require('../../../utils/ApiError');
const { client: redisClient, set: redisSet, get: redisGet, setHash: redisSetHash, getHash: redisGetHash, getAllHash: redisGetAllHash } = require('../../../services/redis.service');
const logger = require('../../../utils/logger');

// Redis key generators
const getMusicKey = (musicId) => `music:${musicId}`;
const getMusicListKey = () => 'music:list';
const getSearchResultsKey = (query) => `music:search:${query}`;

// JioSaavn API endpoints
const JIOSAAVN_API = 'https://saavn.dev/api';

// Search songs from JioSaavn
const searchSongs = async ({query, page = 1, limit = 20}) => {
    try {
        // Try to get from cache first
        // If not in cache, fetch from JioSaavn API
        if(!query || query === ""){
            query = "Trending"
        }
        const response = await axios.get(`${JIOSAAVN_API}/search/songs?query=${encodeURIComponent(query)}`);
        
        if (!response.data || !response.data.data || !response.data.data.results) {
            throw new ApiError(httpStatus.NOT_FOUND, 'No songs found');
        }

        // Transform the response to match our format
        const songs = response.data.data.results.map(song => ({
            id: song.id,
            title: song.name,
            artist: song.primaryArtists,
            duration: song.duration,
            file_url: song.downloadUrl[4]?.url || song.downloadUrl[3]?.url || song.downloadUrl[2]?.url,
            thumbnail_url: song.image[2]?.url || song.image[1]?.url || song.image[0]?.url,
            album: song.album?.name,
            language: song.language,
            year: song.year,
            copyright: song.copyright,
            has_lyrics: song.hasLyrics
        }));

        // Cache the results for 1 hour

        // Apply pagination
        const start = (page - 1) * limit;
        const end = start + limit;

        return {
            total: songs.length,
            songs: songs.slice(start, end),
            page,
            limit
        };
    } catch (error) {
        throw new ApiError(
            error.response?.status || httpStatus.INTERNAL_SERVER_ERROR,
            error.response?.data?.message || 'Error searching songs'
        );
    }
};

// Get song details from JioSaavn
const getSongDetails = async (songId) => {
    try {
        // Try to get from cache first
        const cacheKey = getMusicKey(songId);
        const cachedSong = await redisGet(cacheKey);
        
        if (cachedSong) {
            return JSON.parse(cachedSong);
        }

        // If not in cache, fetch from JioSaavn API
        const response = await axios.get(`${JIOSAAVN_API}/songs?id=${songId}`);
        
        if (!response.data || !response.data.data || !response.data.data[0]) {
            throw new ApiError(httpStatus.NOT_FOUND, 'Song not found');
        }

        const song = response.data.data[0];
        
        // Transform the response to match our format
        const songDetails = {
            id: song.id,
            title: song.name,
            artist: song.primaryArtists,
            duration: song.duration,
            file_url: song.downloadUrl[4]?.link || song.downloadUrl[3]?.link || song.downloadUrl[2]?.link,
            thumbnail_url: song.image[2]?.link || song.image[1]?.link || song.image[0]?.link,
            album: song.album?.name,
            language: song.language,
            year: song.year,
            copyright: song.copyright,
            has_lyrics: song.hasLyrics,
            lyrics: song.lyrics,
            release_date: song.releaseDate,
            label: song.label
        };

        // Cache the song details for 1 day
        await redisSet(cacheKey, JSON.stringify(songDetails), 'EX', 86400);

        return songDetails;
    } catch (error) {
        logger.error('Error getting song details:', error);
        throw new ApiError(
            error.response?.status || httpStatus.INTERNAL_SERVER_ERROR,
            error.response?.data?.message || 'Error getting song details'
        );
    }
};

// Get trending songs
const getTrendingSongs = async (page = 1, limit = 20) => {
    try {
        const cacheKey = 'music:trending';
        const cachedTrending = await redisGet(cacheKey);
        
        if (cachedTrending) {
            const songs = JSON.parse(cachedTrending);
            const start = (page - 1) * limit;
            const end = start + limit;
            return {
                total: songs.length,
                songs: songs.slice(start, end),
                page,
                limit
            };
        }

        // Fetch trending songs from JioSaavn API
        const response = await axios.get(`${JIOSAAVN_API}/modules?language=hindi`);
        
        if (!response.data || !response.data.data || !response.data.data.trending || !response.data.data.trending.songs) {
            throw new ApiError(httpStatus.NOT_FOUND, 'No trending songs found');
        }

        // Transform the response to match our format
        const songs = response.data.data.trending.songs.map(song => ({
            id: song.id,
            title: song.name,
            artist: song.primaryArtists,
            duration: song.duration,
            file_url: song.downloadUrl?.[4]?.link || song.downloadUrl?.[3]?.link || song.downloadUrl?.[2]?.link,
            thumbnail_url: song.image?.[2]?.link || song.image?.[1]?.link || song.image?.[0]?.link,
            album: song.album?.name,
            language: song.language,
            year: song.year,
            copyright: song.copyright,
            has_lyrics: song.hasLyrics
        }));

        // Cache the trending songs for 1 hour
        await redisSet(cacheKey, JSON.stringify(songs), 'EX', 3600);

        // Apply pagination
        const start = (page - 1) * limit;
        const end = start + limit;

        return {
            total: songs.length,
            songs: songs.slice(start, end),
            page,
            limit
        };
    } catch (error) {
        logger.error('Error getting trending songs:', error);
        throw new ApiError(
            error.response?.status || httpStatus.INTERNAL_SERVER_ERROR,
            error.response?.data?.message || 'Error getting trending songs'
        );
    }
};

module.exports = {
    searchSongs,
    getSongDetails,
    getTrendingSongs
}; 