const express = require('express');
// const auth = require('../../../middlewares/auth');
const validate = require("../../../helpers/validate");
const musicValidation = require('../validations/music.validation');
const musicController = require('../controllers/music.controller');
const { authorize } = require("../../../authwires/auth");

const router = express.Router();

router.use(authorize);

/**
 * @swagger
 * /music/search:
 *   get:
 *     summary: Search songs from JioSaavn
 *     tags:
 *       - Music
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: query
 *         in: query
 *         required: false
 *         schema:
 *           type: string
 *         description: Search query for songs
 *       - name: page
 *         in: query
 *         required: false
 *         schema:
 *           type: number
 *         description: Page number for pagination
 *       - name: limit
 *         in: query
 *         required: false
 *         schema:
 *           type: number
 *         description: Number of songs per page
 *     responses:
 *       200:
 *         description: Songs retrieved successfully
 *       400:
 *         description: Bad Request
 *       500:
 *         description: Server Error
 */
router.get('/search', validate(musicValidation.searchSongs), musicController.searchSongs);

/**
 * @swagger
 * /music/trending:
 *   get:
 *     summary: Get trending songs
 *     tags:
 *       - Music
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: page
 *         in: query
 *         required: false
 *         schema:
 *           type: number
 *         description: Page number for pagination
 *       - name: limit
 *         in: query
 *         required: false
 *         schema:
 *           type: number
 *         description: Number of songs per page
 *     responses:
 *       200:
 *         description: Trending songs retrieved successfully
 *       400:
 *         description: Bad Request
 *       500:
 *         description: Server Error
 */
router.get('/trending', validate(musicValidation.getTrendingSongs), musicController.getTrendingSongs);

/**
 * @swagger
 * /music/{id}:
 *   get:
 *     summary: Get song details by ID
 *     tags:
 *       - Music
 *     security:
 *       - Token: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: Song ID from JioSaavn
 *     responses:
 *       200:
 *         description: Song details retrieved successfully
 *       400:
 *         description: Bad Request
 *       404:
 *         description: Song not found
 *       500:
 *         description: Server Error
 */
router.get('/:id', validate(musicValidation.getSongDetails), musicController.getSongDetails);

module.exports = router; 