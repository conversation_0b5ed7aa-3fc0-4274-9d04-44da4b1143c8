const Sib = require("sib-api-v3-sdk")
const emailConfig = require("../configuration/email")();
const Email = require('email-templates');
const path = require('path')

module.exports.sendEmail = async (recipient,subject,tplName, html) => {

    const templateDir = path.join(__dirname, "../Views/", 'email-templates', tplName + '/html')
    const email = new Email({
        views: {
            root: templateDir,
            options: {
                extension: 'ejs'
            }
        }
    });

    const getMailBody = await email.render(templateDir, html);

    const sender = {
        email:"<EMAIL>",
        name:"Pippan Technologies Private Limited"
    }
    
    const recievers = []
    recievers.push({
      email: recipient
    })
    
    const mailData = {
      sender,
      to:recievers,
      subject:subject,
      textContent: getMailBody
    }
    
      emailConfig.sendTransacEmail(mailData,(err)=>{
          if (err) {
              return reject(err);
          }
          return resolve();
        })
    

}


// const sgMail = require('@sendgrid/mail');
// const emailConfig = require("../configuration/email")();
// const Email = require('email-templates');
// const path = require('path');

// module.exports.sendEmail = async (recipient, subject, tplName, html) => {
//     try {
//         const templateDir = path.join(__dirname, "../Views/", 'email-templates', tplName + '/html');
//         const email = new Email({
//             views: {
//                 root: templateDir,
//                 options: {
//                     extension: 'ejs'
//                 }
//             }
//         });

//         const getMailBody = await email.render(templateDir, html);

//         const msg = {
//             to: recipient,
//             from: {
//                 email: "<EMAIL>",
//                 name: "Pippan Technologies Private Limited"
//             },
//             subject: subject,
//             html: getMailBody
//         };

//         await emailConfig.send(msg);
//         return Promise.resolve();
//     } catch (error) {
//         return Promise.reject(error);
//     }
// };