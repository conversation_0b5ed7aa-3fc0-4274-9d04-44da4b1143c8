const { expect } = require('chai');
const io = require('socket.io-client');

describe('Socket.IO Server', function () {
  this.timeout(5000); // Test timeout বাড়ানো হলো

  let socket;

  afterEach(() => {
    if (socket && socket.connected) {
      socket.disconnect();
    }
  });

  it('should connect to the server', function (done) {
    socket = io("http://localhost:8001");

    socket.on('connect', function () {
      expect(socket.connected).to.be.true;
      done();
    });
  });

  it('should join a user room', function (done) {
    socket = io("http://localhost:8001");

    socket.on('connect', function () {
      socket.emit('join', 1); // ✅ emit 'join'
    });

    socket.on('joined', function (message) {
      expect(message).to.equal('User 1 joined their room'); // ✅ expect server response
      done();
    });
  });
});
