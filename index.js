const cluster = require("node:cluster");
const os = require("os");
const totalCPUs = os.cpus().length;

// if (cluster.isPrimary) {
//     console.log(`Master ${process.pid} is running`);
  
//     // Fork workers for each CPU core
//     for (let i = 0; i < totalCPUs; i++) {
//       cluster.fork();
//     }
  
//     // Respawn worker if it exits
//     cluster.on("exit", (worker, code, signal) => {
//       console.log(`Worker ${worker.process.pid} died. Starting a new worker...`);
//       cluster.fork();
//     });
//   } else {

const express = require('express');
const app = express();
const cors = require('cors');
// const mongoose = require('mongoose');
const config = require('./configuration/config');
const port = config.serverPort || 8080;
const router = require('./routes');
const multer = require('multer');
const path = require('path');
const { join, resolve } = require('path');
const { swaggerUi, swaggerSpec } = require('./swagger/swagger'); // Import Swagger
const swaggerAdmin = require(resolve(join(__dirname, 'swagger', 'swagger')));
const {sendEmail} = require("./utils/email");
const { sendMobileVerificationsms } = require("./helpers/sms");
const http = require("http");
const db = require("./models")
const passport = require("passport");
const GoogleStrategy = require("passport-google-oauth20").Strategy;
const FacebookStrategy = require("passport-facebook").Strategy;
const {admin} = require("./configuration/firebase-config")
const fs = require("fs")
const socketIo = require("socket.io");
const { initSocket } = require("./helpers/socket");
const { initializeNeo4j } = require("./configuration/neo4j-config");

app.use(passport.initialize());
initializeNeo4j();

passport.use(
  new GoogleStrategy({
    clientID:process.env.GOOGLE_CLIENT_ID,
    clientSecret:process.env.GOOGLE_CLIENT_SECRECT, 
    callbackURL: "https://pipaan.com360degree.com/api/auth/google-callback", 
    scope: ["profile", "email"]
  },
  async (accessToken, refreshToken, profile, done) => {
   return done(null, profile);
  }
)
);

passport.use(
    new FacebookStrategy(
      {
        clientID: process.env.FACEBOOK_APP_ID,
        clientSecret: process.env.FACEBOOK_APP_SECRET,
        callbackURL: "https://pipaan.com360degree.com/api/auth/facebook-callback",
        profileFields: ["id", "displayName", "email", "photos"]
      },
      (accessToken, refreshToken, profile, done) => {
        return done(null, profile); // No need to store in DB, just return profile
      }
    )
  );

// passport.serializeUser((user, done) => {
//   done(null, user); 
// });

// passport.deserializeUser(async (user, done) => {
//   try {
//     // const user = await User.findById(id);
//     done(null, user); 
//   } catch (error) {
//     done(error, null);
//   }
// });


const CLIENT_ID = "com.pipaan-service"; // Service ID
const TEAM_ID = "4MCGAH3G26";
const KEY_ID = "G8CPY49279";
const REDIRECT_URI = "https://pipaan.com360degree.com/api/auth/apple-callback";
const PRIVATE_KEY_PATH = path.join(__dirname, "AuthKey.p8");

// Read private key
const PRIVATE_KEY = fs.readFileSync(PRIVATE_KEY_PATH, "utf8");

// Generate client secret (JWT)
function generateClientSecret() {
    return jwt.sign(
        {
            iss: TEAM_ID,
            iat: Math.floor(Date.now() / 1000),
            exp: Math.floor(Date.now() / 1000) + 15777000, // 6 months expiry
            aud: "https://appleid.apple.com",
            sub: CLIENT_ID,
        },
        PRIVATE_KEY,
        {
            algorithm: "ES256",
            keyid: KEY_ID,
        }
    );
}

// Apple login route
app.get("/api/auth/apple-login", (req, res) => {
    const url = `https://appleid.apple.com/auth/authorize?response_type=code&client_id=${CLIENT_ID}&redirect_uri=${encodeURIComponent(REDIRECT_URI)}&scope=name%20email&response_mode=form_post`;
    res.redirect(url);
});

// Apple callback route
app.post("/api/auth/apple-callback", async (req, res) => {
    const { code } = req.body;
    if (!code) return res.status(400).send("No authorization code found");

    try {
        const clientSecret = generateClientSecret();

        const postData = qs.stringify({
            client_id: CLIENT_ID,
            client_secret: clientSecret,
            code: code,
            grant_type: "authorization_code",
            redirect_uri: REDIRECT_URI,
        });

        const tokenResponse = await axios.post("https://appleid.apple.com/auth/token", postData, {
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
            },
            timeout: 10000, // 10 seconds timeout
        });

        const { access_token, refresh_token, id_token } = tokenResponse.data;

        if (!id_token) {
            throw new Error("No id_token received from Apple.");
        }

        const decodedToken = jwt.decode(id_token);

        res.json({
            message: "Login successful",
            user: decodedToken,
            tokens: { access_token, refresh_token },
        });

    } catch (error) {
        if (error.response) {
            console.error("Apple Error Response:", error.response.data);
            res.status(500).json({
                message: "Authentication failed",
                error: error.response.data,
            });
        } else {
            console.error("Error:", error.message);
            res.status(500).json({
                message: "Authentication failed",
                error: error.message,
            });
        }
    }
});

  
// Google OAuth routes
app.get("/api/auth/google", passport.authenticate("google", { scope: ["profile", "email"] }));


app.get(
    "/api/auth/facebook",
    passport.authenticate("facebook", { scope: ["email"] })
  );
//  router.get(
//     "/api/auth/google-callback",
//     passport.authenticate("google", { session: false }),
//     (req, res) => {
//       res.json(req.user);
//     }
//   );

global.extractBaseUrl = (fullUrl) => {
    const url = new URL(fullUrl); // Parse the full URL
    return `${url.protocol}//${url.hostname}`; // Construct the base URL
};

global._isAllNumbers = (str)  => {
  return /^\d+$/.test(str);
}


global._isObjectId = (str) => {
    const hexPattern = /^[a-fA-F0-9]{24}$/;
    return hexPattern.test(str);
  };
  

const server = require('http').createServer(app);
const io = require('socket.io')(server, {
  cors: {
    origin: "*",
  }
});

initSocket(io);
// mongoose.connect(config.mongoose.url, config.mongoose.options);

app.use(cors());
// app.use(passport.initialize());
app.use(express.json());

// const server = http.createServer(app);
// Serve Swagger docs

app.use('/', swaggerAdmin.router);

// Use dynamically loaded routes
app.use('/api', router);

app.use(express.static(path.join(__dirname, '../')));

const timeoutMiddleware = (req, res, next) => {
    const timeout = setTimeout(() => {
      res.status(404).json({ success: false, message: "Request timed out" });
    }, 58000); // Set timeout for 58 seconds
    
    // Clear timeout if response is sent before timeout
    res.on("finish", () => {
      clearTimeout(timeout);
    });

    next();
  };

app.use(timeoutMiddleware)

// sendMobileVerificationsms("8637090330" , "123456")
// sendEmail(
//   "<EMAIL>",
//   `Verification Mail || Sociohood`,
//   "verification-mail",
//   {otp: "123456" , token: "fwudfwu"}
// )

app.get('/api/videos/:videoName', (req, res) => {
    const videoPath = path.join(__dirname, '../uploads/videos/', req.params.videoName);
    res.sendFile(videoPath);
});

app.get('/api/profile-pic/:picName', (req, res) => {
    const profilePic = path.join(__dirname, '../uploads/', req.params.picName);
    res.sendFile(profilePic);
});

const MediaProcessor = require("./helpers/mediaprocess");

// Your JSON data
const jsonData = {
    "file_url": "http://aac.saavncdn.com/223/7eddc0f9b56f110ae39a145752fabb34_320.mp4",
    "startTime": "01:00",
    "end_time": "01:30",
    "output_format": "mp3"
};

async function testMediaProcessing() {
    try {
        console.log('🚀 Starting Media Processing Test...');
        console.log('📋 Input Data:', JSON.stringify(jsonData, null, 2));
        
        const mediaProcessor = new MediaProcessor();
        
        console.log('\n⏳ Processing media file...');
        const result = await mediaProcessor.processMediaFromJson(jsonData);
        console.log(result)
        
        console.log('\n✅ Media Processing Completed Successfully!');
        console.log('📊 Results:');
        console.log(`   - Output Filename: ${result.outputFilename}`);
        console.log(`   - Duration: ${result.duration} seconds`);
        console.log(`   - Start Time: ${result.startTime}`);
        console.log(`   - End Time: ${result.endTime}`);
        console.log(`   - Output Path: ${result.outputPath}`);
        
        console.log('\n🎉 Test completed successfully!');
        
    } catch (error) {
        console.error('\n❌ Error during media processing:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

// Run the test
// testMediaProcessing(); 



db.sequelize.sync({ alter: true }).then(() => { 
    server.listen(port, () => {
        console.log(`server listening over http on port ${port}`);
    });
})

app.use((err, req, res, next) => {
    const { statusCode = 400, message = 'Something went wrong!' } = err;
    res.status(statusCode).json({ message, success: false });
});
// }