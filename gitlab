  - deploy

# The deploy job that will run after code is merged to the dev branch or any other branch.
deploy:
  stage: deploy
  only:
    - dev  # Trigger this pipeline only on pushes to the 'dev' branch
  script:
    # Step 1: Install dependencies (if you use npm, yarn, etc.)
    - npm install  # or any other commands to install dependencies like yarn install
    
    # Step 2: Pull the latest changes from the repository
    - git pull origin dev
    
    # Step 3: Restart the application using PM2
    - pm2 restart pipaan-node  # or the command to restart your PM2 app
  environment:
    name: development
    url: https://pipaan.com360degree.com/