'use strict';

const fs = require('fs');
const path = require('path');
const Sequelize = require('sequelize');
const process = require('process');
const basename = path.basename(__filename);
const env = process.env.NODE_ENV || 'development';
const {databaseCredentials} = require(__dirname + '/../configuration/db-config.js');
const config = databaseCredentials[env];
const db = {};
console.log(config)
let sequelize;
if (config.use_env_variable) {
  sequelize = new Sequelize(process.env[config.use_env_variable], config);
} else {
  sequelize = new Sequelize(config.database, config.username, config.password, config);
}

// Define the modules directory
const modulesDir = path.join(__dirname, '../modules');

// Function to load models from all module directories
fs.readdirSync(modulesDir).forEach(module => {
  const modelDir = path.join(modulesDir, module, 'models');
  
  if (fs.existsSync(modelDir) && fs.lstatSync(modelDir).isDirectory()) {
    fs.readdirSync(modelDir)
      .filter(file => file.endsWith('.model.js'))
      .forEach(file => {
        console.log(file);
        const modelPath = path.join(modelDir, file);
        const modelModule = require(modelPath);
        
        // Check if the model is a Sequelize model (function) or Neo4j model (class)
        if (typeof modelModule === 'function' && !modelModule.prototype) {
          // Sequelize model (function that returns a model)
          const model = modelModule(sequelize, Sequelize.DataTypes);
          db[model.name] = model;
        } else if (modelModule.prototype && modelModule.prototype.constructor) {
          // Neo4j model (class)
          const modelName = file.replace('.model.js', '');
          db[modelName] = modelModule;
        }
      });
  }
});

// Associate models if required
Object.keys(db).forEach(modelName => {
  if (db[modelName].associate) {
    db[modelName].associate(db);
  }
});

db.sequelize = sequelize;
db.Sequelize = Sequelize;

module.exports = db;
